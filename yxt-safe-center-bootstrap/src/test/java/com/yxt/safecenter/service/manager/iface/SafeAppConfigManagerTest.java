package com.yxt.safecenter.service.manager.iface;

import com.yxt.safecenter.BaseTest;
import com.yxt.safecenter.common.model.bo.SafeAppConfigUpOrAddBO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class SafeAppConfigManagerTest extends BaseTest {
    @Autowired
    private SafeAppConfigManager manager;


    @Test
    public void testGetByAppKey() {
        manager.getByAppKey("100123");
    }

    @Test
    public void testSave() {
        SafeAppConfigUpOrAddBO safeAppConfigUpOrAddReq = new SafeAppConfigUpOrAddBO();
        safeAppConfigUpOrAddReq.setAppKey("100123");
        safeAppConfigUpOrAddReq.setAppName("POS");
        manager.save(safeAppConfigUpOrAddReq);
    }
    @Test
    public void testUpdateById() {
        SafeAppConfigUpOrAddBO safeAppConfigUpOrAddReq = new SafeAppConfigUpOrAddBO();
        safeAppConfigUpOrAddReq.setId(1L);
        safeAppConfigUpOrAddReq.setAppKey("100123");
        safeAppConfigUpOrAddReq.setAppName("22222");
        manager.updateById(safeAppConfigUpOrAddReq);
    }
    @Test
    public void testDel() {
        manager.del("100123");
    }
}