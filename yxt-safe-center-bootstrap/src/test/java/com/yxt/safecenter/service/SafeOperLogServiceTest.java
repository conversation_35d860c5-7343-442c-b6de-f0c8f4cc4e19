package com.yxt.safecenter.service;

import com.yxt.safecenter.BaseTest;
import com.yxt.safecenter.common.model.dto.req.SafeOperLogQueryReq;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class SafeOperLogServiceTest extends BaseTest {

    @Autowired
    private SafeOperLogService safeOperLogService;

    @Test
    public void testCleanOperLog() {
        safeOperLogService.cleanOperLog();
    }

    @Test
    public void testPage() {
        SafeOperLogQueryReq safeOperLogQueryReq = new SafeOperLogQueryReq();
        safeOperLogService.page(safeOperLogQueryReq);
    }
}