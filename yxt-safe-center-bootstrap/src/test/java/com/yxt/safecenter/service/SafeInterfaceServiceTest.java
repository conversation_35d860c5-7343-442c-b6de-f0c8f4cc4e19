package com.yxt.safecenter.service;

import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.safecenter.BaseTest;
import com.yxt.safecenter.common.model.dto.resp.AuthSelectTreeResp;
import com.yxt.safecenter.common.model.enums.AuthModeEnum;
import com.yxt.safecenter.common.model.enums.RbacValidationEnum;
import com.yxt.safecenter.service.model.bo.SafeInterfaceEBO;
import com.yxt.safecenter.service.model.bo.SafeInterfaceOnlineBO;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceUpReq;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Transactional // 确保测试数据在测试结束后回滚
public class SafeInterfaceServiceTest extends BaseTest {

    @Autowired
    private SafeInterfaceService safeInterfaceService;

    @Test
    public void testOnlineFix() {
        String content = "[{\"applicationName\":\"yxt-safe-center\",\"apiClass\":\"DemoBController\",\"apiMethod\":\"getByIdFromMysql\",\"apiWay\":[\"POST\"],\"apiPath\":[\"/b/demo/getById\",\"/b/demo/xxxx\",\"/b/demo/xxxxada\"]},{\"applicationName\":\"yxt-safe-center\",\"apiClass\":\"DemoBController\",\"apiMethod\":\"getByIdFromMysql\",\"apiWay\":[\"POST\"],\"apiPath\":[\"/b/demo/getByIdS\"]},{\"applicationName\":\"yxt-safe-center\",\"apiClass\":\"DemoCController\",\"apiMethod\":\"getByIdFromMysql\",\"apiWay\":[\"POST\"],\"apiPath\":[\"/c/demo/getById\"]},{\"applicationName\":\"yxt-safe-center\",\"apiClass\":\"DemoThirdController\",\"apiMethod\":\"getByIdFromMysql\",\"apiWay\":[\"POST\"],\"apiPath\":[\"/third/demo/getById\"]},{\"applicationName\":\"yxt-safe-center\",\"apiClass\":\"ErrorController\",\"apiMethod\":\"getOrder\",\"apiWay\":[\"GET\"],\"apiPath\":[\"/api/orders/{orderId}\",\"/api/orders/default2/\",\"/api/orders/default4/\",\"/api/v2/orders/{orderId}\",\"/api/v2/orders/default2/\",\"/api/v2/orders/default4/\"]},{\"applicationName\":\"yxt-safe-center\",\"apiClass\":\"ErrorController\",\"apiMethod\":\"xxxx\",\"apiWay\":[\"GET\",\"POST\",\"PUT\",\"DELETE\"],\"apiPath\":[\"/api/orders/{xxxx}\",\"/api/v2/orders/{xxxx}\"]},{\"applicationName\":\"yxt-safe-center\",\"apiClass\":\"ErrorController\",\"apiMethod\":\"getOrderzcj\",\"apiWay\":[\"GET\"],\"apiPath\":[\"/api/orders/zcj/{orderId}\",\"/api/v2/orders/zcj/{orderId}\"]},{\"applicationName\":\"yxt-safe-center\",\"apiClass\":\"ErrorController\",\"apiMethod\":\"createOrder\",\"apiWay\":[\"GET\"],\"apiPath\":[\"/api/orders/orderId\",\"/api/orders/default\",\"/api/v2/orders/orderId\",\"/api/v2/orders/default\"]},{\"applicationName\":\"yxt-safe-center\",\"apiClass\":\"ErrorController\",\"apiMethod\":\"deleteMapping\",\"apiWay\":[\"DELETE\"],\"apiPath\":[\"/api/orders\",\"/api/v2/orders\"]},{\"applicationName\":\"yxt-safe-center\",\"apiClass\":\"ErrorController\",\"apiMethod\":\"putMapping\",\"apiWay\":[\"PUT\"],\"apiPath\":[\"/api/orders\",\"/api/v2/orders\"]}]";

        safeInterfaceService.online(JSON.parseArray(content, SafeInterfaceOnlineBO.class));
    }

    @Test
    public void testOnline() {
        // 创建测试用的 SafeInterfaceOnlineBO 对象
        SafeInterfaceOnlineBO bo1 = new SafeInterfaceOnlineBO();
        bo1.setApplicationName("app1");
        bo1.setApiClass("DemoController");
        bo1.setApiMethod("getById");
        bo1.setApiWay("GET");
        bo1.setApiPath("[\"/demo/getById\"]");

        SafeInterfaceOnlineBO bo2 = new SafeInterfaceOnlineBO();
        bo2.setApplicationName("app1");
        bo2.setApiClass("DemoController");
        bo2.setApiMethod("getAll");
        bo2.setApiWay("POST");
        bo2.setApiPath("[\"/demo/getAll\"]");

        List<SafeInterfaceOnlineBO> onlineBOList = Arrays.asList(bo1, bo2);

        // 调用服务方法
        safeInterfaceService.online(onlineBOList);

        // 验证数据是否正确插入
        List<SafeInterfaceResp> results = safeInterfaceService.page("", new SafeInterfaceQueryReq()).getData();
        assertNotNull(results);
        assertTrue(results.size() >= 2);
    }

    @Test
    public void testListApplicationName() {
        // 调用服务方法
        List<String> applicationNames = safeInterfaceService.listApplicationName(null);

        // 验证结果是否符合预期
        assertNotNull(applicationNames);
        assertTrue(applicationNames.contains("app1"));
    }

    @Test
    public void testPage() {
        // 创建测试用的查询请求
        SafeInterfaceQueryReq queryReq = new SafeInterfaceQueryReq();
        queryReq.setPageSize(10L);
        queryReq.setCurrentPage(1L);

        // 调用服务方法
        PageDTO<SafeInterfaceResp> page = safeInterfaceService.page("", queryReq);

        // 验证结果是否符合预期
        assertNotNull(page);
        assertTrue(page.getData().size() > 0);
    }

    @Test
    public void testUpBase() {
        // 创建测试用的更新请求
        SafeInterfaceUpReq updateReq = new SafeInterfaceUpReq();
        updateReq.setId(1L);
        updateReq.setApiDesc("Updated Description");

        // 调用服务方法
        safeInterfaceService.upBase(updateReq);

        // 验证更新是否成功
        SafeInterfaceResp result = safeInterfaceService.page("", new SafeInterfaceQueryReq()).getData().stream()
                .filter(resp -> resp.getId().equals(1L))
                .findFirst()
                .orElse(null);

        assertNotNull(result);
        assertEquals("Updated Description", result.getApiDesc());
    }

    @Test
    public void testUpBaseBatch() {
        // 创建测试用的更新请求
        SafeInterfaceUpReq updateReq = new SafeInterfaceUpReq();
        updateReq.setId(1L);
//        updateReq.setAuthMode(AuthModeEnum.APP);

        SafeInterfaceUpReq updateReq1 = new SafeInterfaceUpReq();
        updateReq1.setId(2L);
//        updateReq1.setRbacValidation(RbacValidationEnum.ALL);
        List<SafeInterfaceUpReq> reqList = new ArrayList<>();
        reqList.add(updateReq);
        reqList.add(updateReq1);
        // 调用服务方法
        safeInterfaceService.upBaseBatch(reqList, "yxt-safe-center");
    }

    @Test
    public void testUpStatus() {
        // 创建测试用的 ID 列表
        List<Long> ids = Arrays.asList(1L, 2L);

        // 调用服务方法
        safeInterfaceService.upStatus(ids, true);

        // 验证状态是否更新成功
        List<SafeInterfaceResp> results = safeInterfaceService.page("", new SafeInterfaceQueryReq()).getData();
        assertNotNull(results);
        for (SafeInterfaceResp resp : results) {
            if (ids.contains(resp.getId())) {
                assertTrue(Boolean.valueOf(resp.getStatus()));
            }
        }
    }

    @Test
    public void testImports() {
        // 创建测试用的导入数据
        SafeInterfaceEBO ebo1 = new SafeInterfaceEBO();
        ebo1.setApiClass("DemoController");
        ebo1.setApiMethod("getById");
        ebo1.setApiWay("GET");
        ebo1.setApiPath("[\"/demo/getById\"]");

        SafeInterfaceEBO ebo2 = new SafeInterfaceEBO();
        ebo2.setApiClass("DemoController");
        ebo2.setApiMethod("getAll");
        ebo2.setApiWay("POST");
        ebo2.setApiPath("[\"/demo/getAll\"]");

        List<SafeInterfaceEBO> imports = Arrays.asList(ebo1, ebo2);

        // 调用服务方法
        safeInterfaceService.imports(imports);

        // 验证数据是否正确插入
        List<SafeInterfaceResp> results = safeInterfaceService.page("", new SafeInterfaceQueryReq()).getData();
        assertNotNull(results);
        assertTrue(results.size() >= 2);
    }

    @Test
    public void getAuthSelectInfoTest() {
        List<AuthSelectTreeResp> authSelectInfo = safeInterfaceService.getAuthSelectInfo();
        System.out.println(authSelectInfo);
    }
}