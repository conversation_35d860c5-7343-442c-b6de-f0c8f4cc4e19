package com.yxt.safecenter.service;

import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.BaseTest;
import com.yxt.safecenter.common.model.dto.req.ApiCallStatisticsReq;
import com.yxt.safecenter.common.model.dto.req.SafeApiCallStatisticsPageReq;
import com.yxt.safecenter.common.model.dto.resp.SafeApiCallStatisticsResp;
import com.yxt.safecenter.common.model.enums.AuthGatewayEnum;
import com.yxt.safecenter.common.model.enums.AuthModeEnum;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Since: 2025/2/13 11:57
 * Author: qs
 */
public class ApiStatisticsServiceTest extends BaseTest {

    @Resource
    private ApiCallStatisticsService apiCallStatisticsService;

    @Test
    // 并发插入测试
    public void concurrentSave() {
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        ExLogger.logger("concurrentSave").info("执行开始");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        CountDownLatch latch = new CountDownLatch(100);
        List<ApiCallStatisticsReq> apiCallStatisticsReqs = genData();
        AtomicInteger count = new AtomicInteger();
        for (int i = 0; i < 100; i++) {
            // 模拟多个服务同时调用
            executorService.execute(() -> {
                try {
                    apiCallStatisticsService.apiStatisticsSaveBatch(apiCallStatisticsReqs);
                } catch (Exception e) {
                    count.getAndIncrement();
                    ExLogger.logger("concurrentSave").error("执行异常", e);
                } finally {
                    latch.countDown();
                }
            });
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        stopWatch.stop();
        ExLogger.logger("concurrentSave").info("执行结束，异常次数：{}，耗时{}ms", count.get(), stopWatch.getLastTaskTimeMillis());
    }

    @Test
    public void testApiStatisticsSaveBatch() {
        // 1. 普通批量
//        apiCallStatisticsService.apiStatisticsSaveBatch(genData());

//        // 2. 不同一批有相同的唯一键，应更新
//        List<ApiCallStatisticsReq> reqList = new ArrayList<>();
//        ApiCallStatisticsReq req = new ApiCallStatisticsReq();
//        req.setApplicationName("a");
//        req.setApiPath("/api/test/");
//        req.setApiMethod("POST");
//        req.setGateway("B");
//        req.setAppKey("a");
//        req.setAuthMode(randomAuthMode());
//        req.setTotalCalls(1);
//        req.setChannelInterceptions(1);
//        req.setRbacInterceptions(1);
//        req.setAuthInterceptions(1);
//        req.setPeriod("20250218170000"); // 设置 period
//        req.setStartTime(LocalDateTime.now());
//        req.setEndTime(LocalDateTime.now().plusHours(1));
//        ApiCallStatisticsReq req1 = new ApiCallStatisticsReq();
//        BeanUtils.copyProperties(req, req1);
//        req1.setApplicationName("b");
//        ApiCallStatisticsReq req2 = new ApiCallStatisticsReq();
//        BeanUtils.copyProperties(req, req2);
//        ApiCallStatisticsReq req3 = new ApiCallStatisticsReq();
//        BeanUtils.copyProperties(req, req3);
//        req3.setApplicationName("c");
//        reqList.add(req);
//        reqList.add(req1);
//        reqList.add(req2);
//        reqList.add(req3);
//        // 一批次插入2条，最终插入3条数据，应用a的数据为2，其它为1
//        apiCallStatisticsService.apiStatisticsSaveBatch(reqList);

        // 3. 同一批有相同的唯一键，应更新
        List<ApiCallStatisticsReq> reqList = new ArrayList<>();
        ApiCallStatisticsReq req = new ApiCallStatisticsReq();
        req.setApplicationName("a");
        req.setApiPath("/api/test/");
        req.setApiMethod("POST");
        req.setGateway(AuthGatewayEnum.API.getCode());
        req.setAppKey("a");
        req.setAuthMode(AuthModeEnum.APP.getCode());
        req.setTotalCalls(1);
        req.setChannelInterceptions(1);
        req.setRbacInterceptions(1);
        req.setAuthInterceptions(1);
        req.setPeriod("20250218170000"); // 设置 period
        req.setStartTime(LocalDateTime.now());
        req.setEndTime(LocalDateTime.now().plusHours(1));
        ApiCallStatisticsReq req1 = new ApiCallStatisticsReq();
        BeanUtils.copyProperties(req, req1);
        req1.setApplicationName("b");
        ApiCallStatisticsReq req2 = new ApiCallStatisticsReq();
        BeanUtils.copyProperties(req, req2);
        ApiCallStatisticsReq req3 = new ApiCallStatisticsReq();
        BeanUtils.copyProperties(req, req3);
        req3.setApplicationName("c");
        reqList.add(req);
        reqList.add(req1);
        reqList.add(req2);
        reqList.add(req3);
        // 一批次插入4条，最终插入3条数据，应用a的数据为2，其它为1
        apiCallStatisticsService.apiStatisticsSaveBatch(reqList);
    }

    public List<ApiCallStatisticsReq> genData() {
        List<ApiCallStatisticsReq> reqList = new ArrayList<>();
        Random random = new Random();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

        for (int i = 0; i < 10000; i++) {
            // 生成当前时间往前随机的整点小时
            LocalDateTime startTime = LocalDateTime.now().minusHours(random.nextInt(24));
            startTime = startTime.withMinute(0).withSecond(0); // 确保是整点
            String period = startTime.format(formatter); // 格式化 period
            LocalDateTime endTime = startTime.plusHours(1); // 结束时间加 1 小时

            ApiCallStatisticsReq req = new ApiCallStatisticsReq();
            req.setApplicationName("App-" + (random.nextInt(5) + 1));
            req.setApiPath("/api/test/" + (random.nextInt(10) + 1));
            req.setApiMethod(random.nextBoolean() ? "GET" : "POST");
            req.setGateway(randomGateway());
            req.setAppKey(UUID.randomUUID().toString().substring(0, 8));
            req.setAuthMode(randomAuthMode());
            req.setTotalCalls(random.nextInt(1000) + 1);
            req.setChannelInterceptions(random.nextInt(50));
            req.setRbacInterceptions(random.nextInt(50));
            req.setAuthInterceptions(1);
            req.setPeriod(period); // 设置 period
            req.setStartTime(startTime);
            req.setEndTime(endTime);

            reqList.add(req);
        }

        // 输出示例数据
        return reqList;
    }

    private static String randomGateway() {
        String[] gateways = {"C", "API", "BIGDATA", "BUS"};
        return gateways[new Random().nextInt(gateways.length)];
    }

    private static String randomAuthMode() {
        String[] authModes = {"SESSION", "SIGN", ""};
        return authModes[new Random().nextInt(authModes.length)];
    }

    @Test
    public void testPage() {
        SafeApiCallStatisticsPageReq pageReq = new SafeApiCallStatisticsPageReq();
        pageReq.setGateway(AuthGatewayEnum.API);
        PageDTO<SafeApiCallStatisticsResp> page = apiCallStatisticsService.page(pageReq);
        System.out.println(JSON.toJSONString(page));
    }

    @Test
    public void testClear() {
        apiCallStatisticsService.clearOldData(LocalDateTime.now().minusDays(30));
    }
}
