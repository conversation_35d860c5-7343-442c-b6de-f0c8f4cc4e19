package com.yxt.safecenter.service;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.safecenter.BaseTest;
import com.yxt.safecenter.common.model.bo.SafeAppConfigUpOrAddBO;
import com.yxt.safecenter.common.model.dto.req.SafeAppAuthInterfaceQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeAppAuthInterfaceUpReq;
import com.yxt.safecenter.common.model.dto.req.SafeAppConfigQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeEnableAppConfigInterfacePageReq;
import com.yxt.safecenter.common.model.dto.resp.SafeAppConfigInterfaceApiResp;
import com.yxt.safecenter.common.model.dto.resp.SafeAppConfigResp;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.HashSet;

import static org.junit.Assert.*;

public class SafeAppConfigServiceTest extends BaseTest {

    @Autowired
    private SafeAppConfigService safeAppConfigService;

    @Test
    public void testPage() {
        // 创建查询请求
        SafeAppConfigQueryReq queryReq = new SafeAppConfigQueryReq();
        queryReq.setPageSize(10L);
        queryReq.setCurrentPage(1L);

        // 调用分页查询方法
        PageDTO<SafeAppConfigResp> page = safeAppConfigService.page(queryReq);

        // 验证分页结果是否正确
        assertNotNull("分页结果不能为空", page);
        assertTrue("分页内容数量应大于等于0", page.getData().size() >= 0);
    }

    @Test
    public void testGetByAppKey() {
        // 假设存在一个已知的 appKey
        String appKey = "test-app-key";

        // 调用 getByAppKey 方法
        SafeAppConfigResp resp = safeAppConfigService.getByAppKey(appKey);

        // 验证返回结果是否正确
        assertNotNull("根据 appKey 查询的结果不能为空", resp);
        assertEquals("返回的 appKey 不匹配", appKey, resp.getAppKey());
    }

    @Test
    public void testSaveOrUp() {
        // 创建一个新的请求对象
        SafeAppConfigUpOrAddBO upOrAddReq = new SafeAppConfigUpOrAddBO();
        upOrAddReq.setAppKey("new-app-key");
        upOrAddReq.setAppName("New App");
        upOrAddReq.setEnable(true);

        // 调用保存方法
        safeAppConfigService.saveOrUp(upOrAddReq);

        // 验证是否保存成功
        SafeAppConfigResp savedResp = safeAppConfigService.getByAppKey(upOrAddReq.getAppKey());
        assertNotNull("保存后应能通过 appKey 查询到结果", savedResp);
        assertEquals("保存的 appName 不匹配", upOrAddReq.getAppName(), savedResp.getAppName());

        // 更新请求对象
        upOrAddReq.setId(savedResp.getId());
        upOrAddReq.setAppName("Updated App");

        // 调用更新方法
        safeAppConfigService.saveOrUp(upOrAddReq);

        // 验证是否更新成功
        SafeAppConfigResp updatedResp = safeAppConfigService.getByAppKey(upOrAddReq.getAppKey());
        assertNotNull("更新后应能通过 appKey 查询到结果", updatedResp);
        assertEquals("更新的 appName 不匹配", upOrAddReq.getAppName(), updatedResp.getAppName());
    }

    @Test(expected = YxtBizException.class)
    public void testSaveOrUpWithInvalidId() {
        // 创建一个带有无效 ID 的请求对象
        SafeAppConfigUpOrAddBO upOrAddReq = new SafeAppConfigUpOrAddBO();
        upOrAddReq.setId(9999L); // 假设 9999 是一个不存在的 ID
        upOrAddReq.setAppKey("non-existent-app-key");

        // 调用保存或更新方法，应抛出异常
        safeAppConfigService.saveOrUp(upOrAddReq);
    }

    @Test
    public void testDel() {
        // 创建一个新的请求对象并保存
        SafeAppConfigUpOrAddBO upOrAddReq = new SafeAppConfigUpOrAddBO();
        upOrAddReq.setAppKey("to-delete-app-key");
        upOrAddReq.setAppName("To Delete App");
        upOrAddReq.setEnable(false);
        safeAppConfigService.saveOrUp(upOrAddReq);

        // 查询保存后的 ID
        SafeAppConfigResp savedResp = safeAppConfigService.getByAppKey(upOrAddReq.getAppKey());
        assertNotNull("保存后应能通过 appKey 查询到结果", savedResp);

        // 调用删除方法
        safeAppConfigService.del(savedResp.getId());

        // 验证是否删除成功
        try {
            safeAppConfigService.getByAppKey(upOrAddReq.getAppKey());
            fail("删除后应无法通过 appKey 查询到结果");
        } catch (Exception e) {
            // 预期的异常
        }
    }

    @Test(expected = YxtBizException.class)
    public void testDelWithEnabledApp() {
        // 创建一个新的请求对象并保存，启用状态为 true
        SafeAppConfigUpOrAddBO upOrAddReq = new SafeAppConfigUpOrAddBO();
        upOrAddReq.setAppKey("enabled-app-key");
        upOrAddReq.setAppName("Enabled App");
        upOrAddReq.setEnable(true);
        safeAppConfigService.saveOrUp(upOrAddReq);

        // 查询保存后的 ID
        SafeAppConfigResp savedResp = safeAppConfigService.getByAppKey(upOrAddReq.getAppKey());
        assertNotNull("保存后应能通过 appKey 查询到结果", savedResp);

        // 调用删除方法，应抛出异常
        safeAppConfigService.del(savedResp.getId());
    }

    @Test
    public void testAuthInterfacePage() {
        // 创建查询请求
        SafeAppAuthInterfaceQueryReq queryReq = new SafeAppAuthInterfaceQueryReq();
        queryReq.setAppKey("test-app-key");
        queryReq.setPageSize(10L);
        queryReq.setCurrentPage(1L);

        // 调用分页查询方法
        PageDTO<SafeInterfaceResp> page = safeAppConfigService.authInterfacePage(queryReq);

        // 验证分页结果是否正确
        assertNotNull("分页结果不能为空", page);
        assertTrue("分页内容数量应大于等于0", page.getData().size() >= 0);
    }

    @Test
    public void testAuthInterfaceUp() {
        // 创建授权接口更新请求
        SafeAppAuthInterfaceUpReq upReq = new SafeAppAuthInterfaceUpReq();
        upReq.setAppKey("test-app-key");
        upReq.setAddInterfaceIds(new HashSet<>(Arrays.asList(39L)));
        upReq.setDelInterfaceIds(new HashSet<>(Arrays.asList(38L)));

        // 调用授权接口更新方法
        safeAppConfigService.authInterfaceUp(upReq);

        // 验证更新是否成功
        PageDTO<SafeInterfaceResp> page = safeAppConfigService.authInterfacePage(new SafeAppAuthInterfaceQueryReq());
        System.out.println(page.getData());
    }

    @Test
    public void testEnableAppConfigInterfacePage() {
        // 创建查询请求
        SafeEnableAppConfigInterfacePageReq queryReq = new SafeEnableAppConfigInterfacePageReq();
        queryReq.setPageSize(10L);
        queryReq.setCurrentPage(1L);

        // 调用分页查询方法
        PageDTO<SafeAppConfigInterfaceApiResp> page = safeAppConfigService.enableAppConfigInterfacePage(queryReq);

        // 验证分页结果是否正确
        assertNotNull("分页结果不能为空", page);
        assertTrue("分页内容数量应大于等于0", page.getData().size() >= 0);
    }
}