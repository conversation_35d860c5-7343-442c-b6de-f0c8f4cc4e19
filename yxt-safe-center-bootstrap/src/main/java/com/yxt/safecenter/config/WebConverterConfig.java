package com.yxt.safecenter.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Configuration
@Slf4j
public class WebConverterConfig implements WebMvcConfigurer {

    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String DATE_PATTERN = "yyyy-MM-dd";

    @Bean
    public Converter<String, LocalDateTime> localDateTimeConverter() {
        return new StringToLocalDateTimeConverter();
    }

    @Bean
    public Converter<String, LocalDate> localDateConverter() {
        return new StringToLocalDateConverter();
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(localDateTimeConverter());

        registry.addConverter(localDateConverter());
    }

    /**
     * 显式定义 LocalDateTime Converter 类
     */
    public static class StringToLocalDateTimeConverter implements Converter<String, LocalDateTime> {

        private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);

        @Override
        public LocalDateTime convert(String source) {
            if (source == null || source.isEmpty()) {
                return null;
            }
            try {
                return LocalDateTime.parse(source, formatter);
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("Invalid date format: " + source, e);
            }
        }
    }

    /**
     * 显式定义 LocalDate Converter 类
     */
    public static class StringToLocalDateConverter implements Converter<String, LocalDate> {

        private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_PATTERN);

        @Override
        public LocalDate convert(String source) {
            if (source == null || source.isEmpty()) {
                return null;
            }
            try {
                return LocalDate.parse(source, formatter);
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("Invalid date format: " + source, e);
            }
        }
    }
}
