package com.yxt.safecenter.service.manager.iface;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.safecenter.common.model.bo.SafeAppConfigUpOrAddBO;
import com.yxt.safecenter.common.model.dto.req.SafeAppConfigQueryReq;
import com.yxt.safecenter.common.model.dto.resp.SafeAppConfigResp;
import com.yxt.safecenter.service.model.redis.BizRedisCacheGroup;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 应用配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface SafeAppConfigManager {

    /**
     * 分页查询
     *
     * @param queryReq
     * @return
     */
    PageDTO<SafeAppConfigResp> page(SafeAppConfigQueryReq queryReq);

    /**
     * 获取应用配置信息
     *
     * @param appKey
     * @return
     */
    @Cacheable(cacheNames = BizRedisCacheGroup.SAFE_APP_CONFIG, key = "#appKey", unless = "#result == null")
    SafeAppConfigResp getByAppKey(String appKey);

    /**
     * 获取应用配置信息
     * @param appKeys appKey集合
     * @return list
     */
    List<SafeAppConfigResp> listByAppKeys(Collection<String> appKeys);

    /**
     * 获取应用配置信息
     *
     * @param id
     * @return
     */
    SafeAppConfigResp getById(Long id);

    /**
     * 新增
     *
     * @param upOrAddReq
     */
    void save(SafeAppConfigUpOrAddBO upOrAddReq);

    /**
     * 该更新
     *
     * @param upOrAddReq
     */
    @CacheEvict(cacheNames = BizRedisCacheGroup.SAFE_APP_CONFIG, key = "#upOrAddReq.appKey")
    void updateById(SafeAppConfigUpOrAddBO upOrAddReq);

    /**
     * 删除
     *
     * @param appKey
     */
    @CacheEvict(cacheNames = BizRedisCacheGroup.SAFE_APP_CONFIG, key = "#appKey")
    void del(String appKey);

    /**
     * 分页查询可用的应用配置
     *
     * @param pageReq 查询参数
     * @return pageDTO
     */
    PageDTO<SafeAppConfigResp> enableAppConfigPage(SafeAppConfigQueryReq pageReq);
}
