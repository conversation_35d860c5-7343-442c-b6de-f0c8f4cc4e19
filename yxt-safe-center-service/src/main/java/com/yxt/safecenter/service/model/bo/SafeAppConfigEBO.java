package com.yxt.safecenter.service.model.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 应用配置Excel导入BO
 *
 * <AUTHOR>
 */
@Data
public class SafeAppConfigEBO {

    @ExcelProperty("应用ID")
    private String appKey;

    @ExcelProperty("应用名称")
    private String appName;

    @ExcelProperty("私钥")
    private String privateKey;

    @ExcelProperty("公钥")
    private String publicKey;

    @ExcelProperty("鉴权密钥")
    private String cerKey;

    @ExcelProperty("鉴权方式")
    private String authMethod;

    @ExcelProperty("生效时间")
    private String startTime;

    @ExcelProperty("失效时间")
    private String endTime;

    @ExcelProperty("是否启用")
    private String enable;
}
