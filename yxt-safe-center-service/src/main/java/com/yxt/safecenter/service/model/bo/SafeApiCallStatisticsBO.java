package com.yxt.safecenter.service.model.bo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 接口数据统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Data
public class SafeApiCallStatisticsBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务名
     */
    private String applicationName;

    /**
     * 实际请求path路径，只有一个值
     */
    private String apiPath;

    /**
     * 实际请求方式，只有一个值
     */
    private String apiMethod;

    /**
     * 实际调用网关 C-小程序网关 API-对外网关  BIGDATA-大数据网关 BUS-B端用户网关
     */
    private String gateway;

    /**
     * 调用的appKey
     */
    private String appKey;

    /**
     * 调用的鉴权模式 SESSION-会话 SIGN-验签 为空不鉴权
     */
    private String authMode;

    /**
     * 应用授权方式
     */
    private String appAuthMethod;

    /**
     * 唯一key
     * period + applicationName + apiPath + apiMethod + gateway + appKey + authMode
     * hash
     */
    private String uniKey;

    /**
     * 总调用次数
     */
    private Integer totalCalls;

    /**
     * 调用渠道不匹配拦截次数
     */
    private Integer channelInterceptions;

    /**
     * rbac鉴权拦截次数
     */
    private Integer rbacInterceptions;

    /**
     * 认证鉴权拦截次数
     */
    private Integer authInterceptions;

    /**
     * 统计周期
     */
    private String period;

    /**
     * 统计时段开始时间
     */
    private LocalDateTime startTime;

    /**
     * 统计时段结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

}
