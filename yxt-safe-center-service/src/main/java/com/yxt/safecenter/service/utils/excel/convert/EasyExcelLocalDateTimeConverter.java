package com.yxt.safecenter.service.utils.excel.convert;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * Since: 2025/2/26 14:24
 * Author: qs
 */

public class EasyExcelLocalDateTimeConverter implements Converter<LocalDateTime> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return LocalDateTime.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public LocalDateTime convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if(null==cellData||!StringUtils.hasLength(cellData.getStringValue())) {
            return null;
        }
        return LocalDateTimeUtil.parse(cellData.getStringValue());
    }

    @Override
    public WriteCellData<LocalDateTime> convertToExcelData(LocalDateTime value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if(null==value) {
            return new WriteCellData<>();
        }
        return new WriteCellData<>(LocalDateTimeUtil.formatNormal(value));
    }
}

