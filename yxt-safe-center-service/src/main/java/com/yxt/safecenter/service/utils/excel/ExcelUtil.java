package com.yxt.safecenter.service.utils.excel;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import com.yxt.lang.dto.PageBase;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.safecenter.service.model.FileEnum;
import com.yxt.safecenter.service.utils.BeanMapper;
import com.yxt.safecenter.service.utils.BeanUtil;
import com.yxt.safecenter.service.utils.CommonUtils;
import com.yxt.safecenter.service.utils.PageFunc;
import com.yxt.safecenter.service.utils.excel.convert.EasyExcelLocalDateConverter;
import com.yxt.safecenter.service.utils.excel.convert.EasyExcelLocalDateTimeConverter;
import com.yxt.safecenter.service.utils.excel.convert.GlobalColumnWidthHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ExcelUtil {

    private ExcelUtil() {
    }

    public static <E> void write(HttpServletResponse resp, Class<?> clz, String fileName,
                                 Iterable<? extends E> elements) throws IOException {
        resp.setContentType("application/vnd.ms-excel;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setHeader("Content-disposition",
                "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

        EasyExcelFactory.write(resp.getOutputStream(), clz)
                .registerWriteHandler(new GlobalColumnWidthHandler(30, clz))
                .sheet("模板")
                .registerConverter(new EasyExcelLocalDateTimeConverter())
                .registerConverter(new EasyExcelLocalDateConverter())
                .doWrite(Lists.newArrayList(elements));
    }

    public static <E> byte[] write2ByteArray(Class<?> clz,
                                             Iterable<? extends E> elements) throws IOException {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            EasyExcelFactory.write(out, clz).sheet().doWrite(Lists.newArrayList(elements));
            return out.toByteArray();
        } catch (Exception e) {
            throw e;
        }
    }

    public static <E> List<E> read(MultipartFile file, Class<E> clz) {
        try {
            return read(file.getInputStream(), clz);
        } catch (Throwable e) {
            log.error("excel导入失败:", e);
            throw new YxtBizException("excel导入失败");
        }
    }

    public static <E> List<E> read(InputStream ins, Class<E> clz) {
        return EasyExcelFactory.read(ins).autoTrim(true).sheet().head(clz).doReadSync();
    }

    public static <T> void exportByFileStream(String filePrefix, Class<?> clz, PageBase pageBase,
                                              PageFunc<T> pageFunc, HttpServletResponse response, Integer total) {
        List<T> data = new ArrayList<>();
        Assert.notNull(pageBase);
        try {
            queryPageData(data, pageBase, pageFunc, total);
            String fileName = CommonUtils.getFileName(filePrefix, FileEnum.EXCEL_XLS.getSuffix());
            ExcelUtil.write(response, clz, fileName, data);
        } catch (Throwable e) {
            log.error("导出文件流失败：", e);
            throw new YxtBizException("导出文件流失败");
        }
    }

    public static <T, V> void exportByFileStream(String filePrefix, Class<V> clz, PageBase pageBase, PageFunc<T> pageFunc, BeanMapper<T, V> beanMapper, HttpServletResponse response) {
        List<V> data = new ArrayList<>();
        Assert.notNull(pageBase);
        try {
            PageDTO<T> pageDTO;
            pageBase.setCurrentPage(1L);
            pageBase.setPageSize(200L);
            do {
                pageDTO = pageFunc.queryAllPage();
                pageBase.setCurrentPage(pageBase.getCurrentPage() + 1);
                data.addAll(BeanUtil.copyList(pageDTO.getData(), clz, (v, t) -> beanMapper.map(t, v)));
            } while (CollectionUtil.isNotEmpty(pageDTO.getData()) && pageDTO.getData().size() >= 200);
            String fileName = CommonUtils.getFileName(filePrefix, FileEnum.EXCEL_XLS.getSuffix());
            ExcelUtil.write(response, clz, fileName, data);
        } catch (Throwable e) {
            log.error("导出文件流失败：", e);
            throw new YxtBizException("导出文件流失败");
        }
    }

    /**
     * 分页查询数据
     *
     * @param <T>
     * @param data
     * @param pageBase
     * @param pageFunc
     * @param total    查询最大条数
     */
    private static <T> void queryPageData(List<T> data, PageBase pageBase, PageFunc<T> pageFunc, Integer total) {
        PageDTO<T> pageDTO;
        pageBase.setCurrentPage(1L);
        pageBase.setPageSize(200L);
        do {
            pageDTO = pageFunc.queryAllPage();
            pageBase.setCurrentPage(pageBase.getCurrentPage() + 1);
            if (CollectionUtil.isNotEmpty(pageDTO.getData())) {
                data.addAll(pageDTO.getData());
            }
        } while (CollectionUtil.isNotEmpty(pageDTO.getData()) && pageDTO.getData().size() >= 200 && data.size() <= total);
    }


    /**
     * 校验表头的读取excel文件
     * 需要表头和EO类的顺序一致，并且名字一摸一样
     * 否则将抛出异常
     */
    public static <E> List<E> readWithCheckedHead(MultipartFile file, Class<E> clz) throws IOException, IllegalAccessException {
        InputStream ins = file.getInputStream();
        return readWithCheckedHead(ins, clz);
    }

    public static <E> List<E> readWithCheckedHead(InputStream ins, Class<E> clz) throws IOException, IllegalAccessException {
        List<E> objs = EasyExcelFactory.read(ins).ignoreEmptyRow(true).autoTrim(true).headRowNumber(0).sheet().head(clz).doReadSync();

        E headRow = objs.remove(0);
        Field[] fields = headRow.getClass().getDeclaredFields();

        for (Field field : fields) {
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (excelProperty != null && excelProperty.value().length >= 1) {
                String annotationName = excelProperty.value()[0];
                field.setAccessible(true);
                String headName = (String) field.get(headRow);

                if (!annotationName.equals(headName)) {
                    throw new YxtBizException("请上传正确的模板");
                }
            }
        }
        return objs;
    }

    public static <E> List<E> readWithCheckedHead2(MultipartFile file, Class<E> clz) throws IOException, IllegalAccessException {
        List<E> objs = EasyExcelFactory.read(file.getInputStream()).ignoreEmptyRow(true).autoTrim(true).headRowNumber(0).sheet().head(clz).doReadSync();

        E headRow = objs.remove(0);
        headRow = objs.remove(0);
        Field[] fields = headRow.getClass().getDeclaredFields();

        for (Field field : fields) {
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (excelProperty != null && excelProperty.value().length >= 1) {
                String annotationName = excelProperty.value()[0];
                field.setAccessible(true);
                String headName = (String) field.get(headRow);

                if (!annotationName.equals(headName)) {
                    throw new YxtBizException("请上传正确的模板");
                }
            }
        }
        return objs;
    }

}
