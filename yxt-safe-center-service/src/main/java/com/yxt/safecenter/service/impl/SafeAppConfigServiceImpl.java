package com.yxt.safecenter.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.asymmetric.RSA;
import com.google.common.collect.Lists;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lang.util.UUIDUtil;
import com.yxt.safecenter.common.model.bo.SafeAppConfigBaseUpBO;
import com.yxt.safecenter.common.model.bo.SafeAppConfigUpOrAddBO;
import com.yxt.safecenter.common.model.dto.req.SafeAppAuthInterfaceQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeAppAuthInterfaceUpReq;
import com.yxt.safecenter.common.model.dto.req.SafeAppConfigQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeEnableAppConfigInterfacePageReq;
import com.yxt.safecenter.service.model.bo.SafeAppConfigEBO;
import com.yxt.safecenter.common.model.dto.resp.SafeAppConfigInterfaceApiResp;
import com.yxt.safecenter.common.model.dto.resp.SafeAppConfigResp;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import com.yxt.safecenter.common.model.enums.AuthMethodEnum;
import com.yxt.safecenter.service.SafeAppConfigService;
import com.yxt.safecenter.service.manager.iface.SafeAppAuthInterfaceManager;
import com.yxt.safecenter.service.manager.iface.SafeAppConfigManager;
import com.yxt.safecenter.service.manager.iface.SafeInterfaceManager;
import com.yxt.safecenter.service.model.bo.SafeAppAuthInterfaceBO;
import com.yxt.safecenter.service.model.bo.SafeInterfaceBO;
import com.yxt.safecenter.service.utils.BeanUtil;
import com.yxt.safecenter.service.utils.PageDtoUtil;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.stream.Collectors;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * .
 *
 * <AUTHOR>
 * @date 2025年02月08日 17:09
 */
@Service
@RequiredArgsConstructor
public class SafeAppConfigServiceImpl implements SafeAppConfigService {

    private final SafeAppConfigManager safeAppConfigManager;
    private final SafeAppAuthInterfaceManager safeAppAuthInterfaceManager;
    private final SafeInterfaceManager safeInterfaceManager;

    @Override
    public PageDTO<SafeAppConfigResp> page(SafeAppConfigQueryReq queryReq) {
        return safeAppConfigManager.page(queryReq);
    }

    @Override
    public SafeAppConfigResp getByAppKey(String appKey) {
        return safeAppConfigManager.getByAppKey(appKey);
    }

    @Override
    public List<SafeAppConfigResp> listByAppKeys(Collection<String> appKeys) {
        return safeAppConfigManager.listByAppKeys(appKeys);
    }

    @Override
    public void saveOrUp(SafeAppConfigUpOrAddBO upOrAddReq) {
        if (upOrAddReq.getAuthMethod() != null) {
            if (upOrAddReq.getAuthMethod().isRsa()) {
                // 自动生成公私钥
                RSA rsa = new RSA();
                upOrAddReq.setPublicKey(rsa.getPublicKeyBase64());
                upOrAddReq.setPrivateKey(rsa.getPrivateKeyBase64());
            } else {
                if (StringUtil.isEmpty(upOrAddReq.getCerKey())) {
                    // 自动生成秘钥
                    upOrAddReq.setCerKey(UUIDUtil.generateUuid());
                }
            }
        }
        if (upOrAddReq.getId() != null) {
            SafeAppConfigResp safeAppConfigResp = safeAppConfigManager.getById(upOrAddReq.getId());
            if (safeAppConfigResp == null) {
                throw new YxtBizException("应用配置不存在");
            }
            upOrAddReq.setAppKey(safeAppConfigResp.getAppKey());
            safeAppConfigManager.updateById(upOrAddReq);
        } else {
            // 是否已经存在
            SafeAppConfigResp safeAppConfigResp = safeAppConfigManager.getByAppKey(upOrAddReq.getAppKey());
            if (safeAppConfigResp != null) {
                throw new YxtBizException("应用配置已存在");
            }
            safeAppConfigManager.save(upOrAddReq);
        }
    }

    @Override
    public void updateBase(SafeAppConfigBaseUpBO upReq) {
        if (upReq.getId() == null) {
            throw new YxtBizException("请传入id");
        }
        SafeAppConfigResp safeAppConfigResp = safeAppConfigManager.getById(upReq.getId());
        if (safeAppConfigResp == null) {
            throw new YxtBizException("应用配置不存在");
        }
        SafeAppConfigUpOrAddBO upOrAddReq = BeanUtil.copyProperties(upReq, SafeAppConfigUpOrAddBO.class);
        upOrAddReq.setAppKey(safeAppConfigResp.getAppKey());
        safeAppConfigManager.updateById(upOrAddReq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(Long id) {
        SafeAppConfigResp safeAppConfigResp = safeAppConfigManager.getById(id);
        if (safeAppConfigResp == null) {
            return;
        }
        if (safeAppConfigResp.getEnable()) {
            throw new YxtBizException("请先停用应用");
        }
        safeAppConfigManager.del(safeAppConfigResp.getAppKey());
        safeAppAuthInterfaceManager.delByAppKey(safeAppConfigResp.getAppKey());
    }

    @Override
    public PageDTO<SafeInterfaceResp> authInterfacePage(SafeAppAuthInterfaceQueryReq authInterfaceQueryReq) {
        PageDTO<SafeAppAuthInterfaceBO> page = safeAppAuthInterfaceManager.page(authInterfaceQueryReq);
        if (CollectionUtils.isEmpty(page.getData())) {
            return PageDtoUtil.convert(page, new ArrayList<>());
        }
        List<Long> interfaceIds = page.getData().stream().map(SafeAppAuthInterfaceBO::getInterfaceId).collect(Collectors.toList());
        // 查询接口信息
        return PageDtoUtil.convert(page, BeanUtil.copyList(safeInterfaceManager.listByIds(interfaceIds), SafeInterfaceResp.class));
    }

    @Override
    public List<Long> authInterfaceIdList(String appKey, String applicationName) {
        return safeAppAuthInterfaceManager.listByAppKeyAndApplicationName(appKey, applicationName)
                .stream().map(SafeAppAuthInterfaceBO::getInterfaceId).collect(Collectors.toList());
    }

    @Override
    public void authInterfaceUp(SafeAppAuthInterfaceUpReq upReq) {
        List<SafeAppAuthInterfaceBO> safeAppAuthInterfaceBOS = safeAppAuthInterfaceManager.listByAppKey(upReq.getAppKey());

        Set<Long> addInterfaceIds = upReq.getAddInterfaceIds();
        if (CollectionUtil.isNotEmpty(addInterfaceIds)) {
            // 过滤已经存在的
            addInterfaceIds.removeAll(safeAppAuthInterfaceBOS.stream().map(SafeAppAuthInterfaceBO::getInterfaceId).collect(Collectors.toSet()));
            if (CollectionUtil.isNotEmpty(addInterfaceIds)) {
                List<SafeInterfaceBO> safeInterfaceBOS = safeInterfaceManager.listByIds(Lists.newArrayList(addInterfaceIds));
                safeAppAuthInterfaceManager.saveBatch(upReq.getAppKey(), safeInterfaceBOS);
            }
        }
        Set<Long> delInterfaceIds = upReq.getDelInterfaceIds();
        if (CollectionUtil.isNotEmpty(delInterfaceIds)) {
            // 删除授权的接口
            safeAppAuthInterfaceManager.delByAppKeyAndInterfaceIds(upReq.getAppKey(), delInterfaceIds);
        }
    }

    @Override
    public PageDTO<SafeAppConfigInterfaceApiResp> enableAppConfigInterfacePage(SafeEnableAppConfigInterfacePageReq pageReq) {
        // 分页查询应用
        PageDTO<SafeAppConfigResp> page = safeAppConfigManager.enableAppConfigPage(BeanUtil.copyProperties(pageReq, SafeAppConfigQueryReq.class));
        if (CollectionUtils.isEmpty(page.getData())) {
            return PageDtoUtil.convert(page, new ArrayList<>());
        } else {
            List<SafeAppConfigInterfaceApiResp> respList = BeanUtil.copyList(page.getData(), SafeAppConfigInterfaceApiResp.class);
            for (SafeAppConfigInterfaceApiResp resp : respList) {
                // 查询授权接口信息
                resp.setInterfaceList(safeAppAuthInterfaceManager.listByAppKey(resp.getAppKey()).stream().map(SafeAppAuthInterfaceBO::getInterfaceId).collect(Collectors.toList()));
            }
            return PageDtoUtil.convert(page, respList);
        }
    }

    @Override
    public void imports(List<SafeAppConfigEBO> imports) {
        if (CollectionUtil.isEmpty(imports)) {
            return;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (SafeAppConfigEBO importItem : imports) {
            // 数据验证
            if (StringUtil.isEmpty(importItem.getAppKey())) {
                throw new YxtBizException("应用ID不能为空");
            }
            if (StringUtil.isEmpty(importItem.getAppName())) {
                throw new YxtBizException("应用名称不能为空，应用ID：" + importItem.getAppKey());
            }
//            if (StringUtil.isEmpty(importItem.getAuthMethod())) {
//                throw new YxtBizException("鉴权方式不能为空，应用ID：" + importItem.getAppKey());
//            }

            // 验证鉴权方式
            AuthMethodEnum authMethodEnum = null;
            try {
                if (!StringUtils.isEmpty(importItem.getAuthMethod())) {
                    authMethodEnum = AuthMethodEnum.valueOf(importItem.getAuthMethod().toUpperCase());
                }
            } catch (IllegalArgumentException e) {
                throw new YxtBizException("鉴权方式不正确，应用ID：" + importItem.getAppKey() + "，支持的鉴权方式：RSA、MD5、TOKENBS");
            }

            // 构建保存或更新对象
            SafeAppConfigUpOrAddBO upOrAddBO = new SafeAppConfigUpOrAddBO();
            upOrAddBO.setAppKey(importItem.getAppKey());
            upOrAddBO.setAppName(importItem.getAppName());
            upOrAddBO.setPrivateKey(importItem.getPrivateKey());
            upOrAddBO.setPublicKey(importItem.getPublicKey());
            upOrAddBO.setCerKey(importItem.getCerKey());
            upOrAddBO.setAuthMethod(authMethodEnum);

            // 处理时间字段
            if (StringUtil.isNotEmpty(importItem.getStartTime())) {
                try {
                    upOrAddBO.setStartTime(LocalDateTime.parse(importItem.getStartTime(), formatter));
                } catch (Exception e) {
                    throw new YxtBizException("生效时间格式不正确，应用ID：" + importItem.getAppKey() + "，正确格式：yyyy-MM-dd HH:mm:ss");
                }
            }
            if (StringUtil.isNotEmpty(importItem.getEndTime())) {
                try {
                    upOrAddBO.setEndTime(LocalDateTime.parse(importItem.getEndTime(), formatter));
                } catch (Exception e) {
                    throw new YxtBizException("失效时间格式不正确，应用ID：" + importItem.getAppKey() + "，正确格式：yyyy-MM-dd HH:mm:ss");
                }
            }

            // 处理启用状态
            if (StringUtil.isNotEmpty(importItem.getEnable())) {
                String enableStr = importItem.getEnable().trim();
                if ("是".equals(enableStr) || "true".equalsIgnoreCase(enableStr) || "1".equals(enableStr)) {
                    upOrAddBO.setEnable(true);
                } else if ("否".equals(enableStr) || "false".equalsIgnoreCase(enableStr) || "0".equals(enableStr)) {
                    upOrAddBO.setEnable(false);
                } else {
                    throw new YxtBizException("是否启用字段值不正确，应用ID：" + importItem.getAppKey() + "，支持的值：是/否、true/false、1/0");
                }
            } else {
                upOrAddBO.setEnable(true); // 默认启用
            }

            // 检查是否存在，进行新增或更新
            SafeAppConfigResp existingConfig = safeAppConfigManager.getByAppKey(importItem.getAppKey());
            if (existingConfig != null) {
                // 更新
                upOrAddBO.setId(existingConfig.getId());
                safeAppConfigManager.updateById(upOrAddBO);
            } else {
                safeAppConfigManager.save(upOrAddBO);
            }

        }
    }

}
