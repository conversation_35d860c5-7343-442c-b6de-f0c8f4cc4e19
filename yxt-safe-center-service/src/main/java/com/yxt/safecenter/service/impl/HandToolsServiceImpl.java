package com.yxt.safecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.yxt.safecenter.common.model.dto.AuthInfoDTO;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceUpReq;
import com.yxt.safecenter.common.model.dto.req.SetAuthInfoReq;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import com.yxt.safecenter.service.HandToolsService;
import com.yxt.safecenter.service.SafeInterfaceService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Since: 2025/05/07 15:25
 * Author: qs
 */

@Service
public class HandToolsServiceImpl implements HandToolsService {

    @Resource
    private SafeInterfaceService safeInterfaceService;

    AntPathMatcher matcher = new AntPathMatcher();

    @Override
    public void setAuthInfo(SetAuthInfoReq req) {
        // 查询服务所有接口
        List<SafeInterfaceResp> safeInterfaceList = safeInterfaceService.listByApplicationName(req.getApplicationName());
        List<SafeInterfaceResp> matchList = new ArrayList<>();
        for (SafeInterfaceResp safeInterfaceResp : safeInterfaceList) {
            List<String> pathList = JSON.parseArray(safeInterfaceResp.getApiPath(), String.class);
            for (String path : pathList) {
                for (String pattern : req.getPathPatternList()) {
                    if (matcher.match(pattern, path)) {
                        matchList.add(safeInterfaceResp);
                        break;
                    }
                }
            }
        }

        // 修改信息
        if (CollectionUtils.isNotEmpty(matchList)) {
            List<SafeInterfaceUpReq> upInterfaceList = new ArrayList<>();
            matchList.forEach(safeInterfaceResp -> {
                SafeInterfaceUpReq upReq = new SafeInterfaceUpReq();
                upReq.setId(safeInterfaceResp.getId());
                upReq.setAuthInfo(req.getAuthInfos());
                upInterfaceList.add(upReq);
            });
            safeInterfaceService.upBaseBatch(upInterfaceList, req.getApplicationName());
        }

    }

    @Override
    public void setAuthInfoNoCover(SetAuthInfoReq req) {
        // 查询服务所有接口
        List<SafeInterfaceResp> safeInterfaceList = safeInterfaceService.listByApplicationName(req.getApplicationName());
        List<SafeInterfaceResp> matchList = new ArrayList<>();
        for (SafeInterfaceResp safeInterfaceResp : safeInterfaceList) {
            List<String> pathList = JSON.parseArray(safeInterfaceResp.getApiPath(), String.class);
            for (String path : pathList) {
                for (String pattern : req.getPathPatternList()) {
                    if (matcher.match(pattern, path)) {
                        matchList.add(safeInterfaceResp);
                        break;
                    }
                }
            }
        }

        // 修改信息
        if (CollectionUtils.isNotEmpty(matchList)) {
            List<SafeInterfaceUpReq> upInterfaceList = new ArrayList<>();
            matchList.forEach(safeInterfaceResp -> {
                SafeInterfaceUpReq upReq = new SafeInterfaceUpReq();
                upReq.setId(safeInterfaceResp.getId());

                List<AuthInfoDTO> authInfoDTOList;
                if (StringUtils.isNotEmpty(safeInterfaceResp.getAuthInfo())) {
                    authInfoDTOList = JSON.parseArray(safeInterfaceResp.getAuthInfo(), AuthInfoDTO.class);
                    Map<String, List<AuthInfoDTO>> oldAuthInfoMap = authInfoDTOList.stream().collect(Collectors.groupingBy(m -> m.getAuthGateway().getName()));
                    for (AuthInfoDTO statistidsAuthInfoDTO : req.getAuthInfos()) {
                        List<AuthInfoDTO> tempOldAuthInfoDTOList = oldAuthInfoMap.get(statistidsAuthInfoDTO.getAuthGateway().getName());
                        if (CollectionUtils.isEmpty(tempOldAuthInfoDTOList)) {
                            // 没有该网关新增一条数据
                            authInfoDTOList.add(statistidsAuthInfoDTO);
                        } else {
                            AuthInfoDTO oldAuthDTO = tempOldAuthInfoDTOList.get(0);
                            if (oldAuthDTO.getAuthMode() == null) {
                                // 没有鉴权模式设置鉴权模式
                                oldAuthDTO.setAuthMode(statistidsAuthInfoDTO.getAuthMode());
                            }
                        }
                    }
                } else {
                    authInfoDTOList = req.getAuthInfos();
                }

                upReq.setAuthInfo(authInfoDTOList);
                upInterfaceList.add(upReq);
            });
            safeInterfaceService.upBaseBatch(upInterfaceList, req.getApplicationName());
        }
    }
}
