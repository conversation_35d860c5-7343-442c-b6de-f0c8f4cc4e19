package com.yxt.safecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.yxt.common.wechatrobot.util.WxRobotOkHttpUtils;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.common.lib.config.SafeCenterRobotConfig;
import com.yxt.safecenter.common.model.bo.SafeAppConfigBaseUpBO;
import com.yxt.safecenter.common.model.dto.AuthInfoDTO;
import com.yxt.safecenter.common.model.dto.req.SafeAppAuthInterfaceUpReq;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceUpReq;
import com.yxt.safecenter.common.model.dto.resp.SafeAppConfigResp;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import com.yxt.safecenter.common.model.enums.AuthGatewayEnum;
import com.yxt.safecenter.common.model.enums.AuthMethodEnum;
import com.yxt.safecenter.common.model.enums.AuthModeEnum;
import com.yxt.safecenter.service.ApiCallStatisticsService;
import com.yxt.safecenter.service.CompleteInterfaceConfigService;
import com.yxt.safecenter.service.SafeAppConfigService;
import com.yxt.safecenter.service.SafeInterfaceService;
import com.yxt.safecenter.service.model.bo.SafeApiCallStatisticsBO;
import com.yxt.safecenter.service.utils.InterfaceMatcherUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Since: 2025/2/19 10:53
 * Author: qs
 */

@Service
public class CompleteInterfaceConfigServiceImpl implements CompleteInterfaceConfigService {

    @Resource
    private SafeInterfaceService interfaceService;
    @Resource
    private ApiCallStatisticsService apiCallStatisticsService;
    @Resource
    private SafeAppConfigService appConfigService;

    @Override
    public void completeInterfaceConfig(String beginTime, String endTime) {
        // 鉴权模式（SESSION-C SESSION-B SIGN 同一接口有多种要告警） 调用网关  应用鉴权模式 应用授权的接口

        if (StringUtils.isEmpty(beginTime) || StringUtils.isEmpty(endTime)) {
            ExLogger.logger("completeInterfaceConfig").error("时间范围为空直接返回：{}，{}", beginTime, endTime);
            return;
        }

        // 查询所有应用
        List<String> applicationNameList = interfaceService.listApplicationName(null);
        int count = 0;
        for (String applicationName : applicationNameList) {
            StopWatch stopWatch = new StopWatch();
            try {
                stopWatch.start();
                ExLogger.logger("completeInterfaceConfig").info("补全接口配置处理应用{}开始", applicationName);
                // 查询应用所有接口
                List<SafeInterfaceResp> interfaceList = interfaceService.listByApplicationName(applicationName);
                // 查询应用所有接口请求记录 筛选掉没有一次成功的数据
                List<SafeApiCallStatisticsBO> apiCallStatisticsList = apiCallStatisticsService.listByApplicationNameAndTime(applicationName, beginTime, endTime)
                        .stream().filter(f -> f.getTotalCalls() != (f.getChannelInterceptions() + f.getRbacInterceptions() + f.getAuthInterceptions()))
                        .collect(Collectors.toList());

                // 需要更新的接口数据
                List<SafeInterfaceUpReq> upInterfaceList = completeInterfaceConfig(applicationName, apiCallStatisticsList, interfaceList, beginTime, endTime);

                // 需要更新的应用数据
                List<SafeAppConfigBaseUpBO> upAppConfigList = new ArrayList<>();
                // 需要新增的应用鉴权接口数据
                List<SafeAppAuthInterfaceUpReq> addAppAuthInterfaceList = new ArrayList<>();
                // 补全应用配置及应用授权接口
                completeAppConfig(applicationName, apiCallStatisticsList, upAppConfigList, interfaceList, addAppAuthInterfaceList, beginTime, endTime);

                // 保存数据库
                if (!upInterfaceList.isEmpty()) {
                    ListUtils.partition(upInterfaceList, 10).forEach(upInterface -> {
                        interfaceService.upBaseBatch(upInterface, applicationName);
                    });
                }
                if (!upAppConfigList.isEmpty()) {
                    for (SafeAppConfigBaseUpBO safeAppConfigBaseUpBO : upAppConfigList) {
                        appConfigService.updateBase(safeAppConfigBaseUpBO);
                    }
                }
                if (!addAppAuthInterfaceList.isEmpty()) {
                    for (SafeAppAuthInterfaceUpReq safeAppAuthInterfaceUpReq : addAppAuthInterfaceList) {
                        appConfigService.authInterfaceUp(safeAppAuthInterfaceUpReq);
                    }
                }

                stopWatch.stop();
            } catch (Exception e) {
                ExLogger.logger("completeInterfaceConfig").error("补全接口配置处理应用{}异常", applicationName, e);
            } finally {
                ExLogger.logger("completeInterfaceConfig").info("补全接口配置处理应用{}完成，耗时:{}ms，进度：{}/{}"
                        , applicationName, stopWatch.getTime(), ++count, applicationNameList.size());
            }
        }
    }

    /**
     * 补全应用配置及授权接口
     *
     * @param applicationName         服务名
     * @param apiCallStatisticsList   接口请求统计记录
     * @param upAppConfigList         需要修改的应用配置列表
     * @param interfaceList           接口列表
     * @param addAppAuthInterfaceList 需要新增的应用鉴权接口列表
     * @param beginTime               统计开始时间
     * @param endTime                 统计结束时间
     */
    private void completeAppConfig(String applicationName, List<SafeApiCallStatisticsBO> apiCallStatisticsList, List<SafeAppConfigBaseUpBO> upAppConfigList
            , List<SafeInterfaceResp> interfaceList, List<SafeAppAuthInterfaceUpReq> addAppAuthInterfaceList, String beginTime, String endTime) {
        // 统计接口按照应用分组
        Map<String, List<SafeApiCallStatisticsBO>> groupByAppKeyMap = apiCallStatisticsList.stream()
                .filter(f -> StringUtils.isNotEmpty(f.getAppKey()) && AuthModeEnum.APP.equals(AuthModeEnum.getByName(f.getAuthMode())))
                .collect(Collectors.groupingBy(SafeApiCallStatisticsBO::getAppKey));
        // 查询应用信息
        Set<String> appKeySet = apiCallStatisticsList.stream().map(SafeApiCallStatisticsBO::getAppKey).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        List<SafeAppConfigResp> safeAppConfigList = appConfigService.listByAppKeys(appKeySet);
        Map<String, SafeAppConfigResp> appConfigMap = safeAppConfigList.stream()
                .filter(v -> StringUtils.isNotEmpty(v.getAppKey()))
                .collect(Collectors.toMap(
                        SafeAppConfigResp::getAppKey,
                        Function.identity(),
                        (existing, replacement) -> replacement
                ));

        groupByAppKeyMap.forEach((key, value) -> {
            SafeAppConfigBaseUpBO appConfigUpReq = null;
            // 处理应用鉴权方式
            SafeAppConfigResp safeAppConfigResp = appConfigMap.get(key);
            if (safeAppConfigResp == null) {
                ExLogger.logger("completeInterfaceConfig").warn("应用鉴权方式应用{}未找到应用，数据周期{}-{}", key, beginTime, endTime);
                WxRobotOkHttpUtils.post(SafeCenterRobotConfig.AUTH_ALERT, String.format("应用补全应用鉴权方式应用编码：%s未找到应用，数据周期%s-%s", key, beginTime, endTime));
                return;
            }

            // 处理鉴权模式
            Set<String> appAuthMethodSet = value.stream().map(SafeApiCallStatisticsBO::getAppAuthMethod).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
            if (appAuthMethodSet.size() > 1) {
                ExLogger.logger("completeInterfaceConfig").warn("应用鉴权方式应用{}存在多种鉴权方式，数据周期{}-{}", key, beginTime, endTime);
                WxRobotOkHttpUtils.post(SafeCenterRobotConfig.AUTH_ALERT, String.format("应用补全 应用鉴权方式应用:%s存在多种鉴权方式，数据周期%s-%s", key, beginTime, endTime));
            } else {
                String appAuthMethod = appAuthMethodSet.iterator().next();
                if (StringUtils.isEmpty(safeAppConfigResp.getAuthMethod())) {
                    appConfigUpReq = new SafeAppConfigBaseUpBO();
                    appConfigUpReq.setAuthMethod(AuthMethodEnum.getByName(appAuthMethod));
                } else if (!safeAppConfigResp.getAuthMethod().equals(appAuthMethod)) {
                    ExLogger.logger("completeInterfaceConfig").warn("应用鉴权方式应用{}鉴权方式{}和数据库中{}不一样，不更新，时间周期{}-{}"
                            , key, appAuthMethod, safeAppConfigResp.getAuthMethod(), beginTime, endTime);
                    WxRobotOkHttpUtils.post(SafeCenterRobotConfig.AUTH_ALERT, String.format("应用补全 应用鉴权方式应用%s鉴权方式%s和数据库中%s不一样，不更新，时间周期%s-%s"
                            , key, appAuthMethod, safeAppConfigResp.getAuthMethod(), beginTime, endTime));
                }
            }

            if (appConfigUpReq != null) {
                appConfigUpReq.setId(safeAppConfigResp.getId());
                upAppConfigList.add(appConfigUpReq);
            }

            // 处理应用授权接口
            Set<Long> addAuthInterfaceIdSet = new HashSet<>();
            // 查询应用授权接口信息
            List<Long> authInterfaceIdList = appConfigService.authInterfaceIdList(key, applicationName);
            // 按照接口唯一标识进行分组
            Map<String, List<SafeApiCallStatisticsBO>> groupByApiAppMap = value.stream()
                    .collect(Collectors.groupingBy(stat -> stat.getApiPath() + "|" + stat.getApiMethod()
                    ));
            groupByApiAppMap.forEach((apiKey, voValue) -> {
                String apiMethod = voValue.get(0).getApiMethod();
                String apiPath = voValue.get(0).getApiPath();
                SafeInterfaceResp matchInterface = InterfaceMatcherUtil.matchApi(interfaceList, apiPath, apiMethod);
                if (matchInterface == null) {
                    ExLogger.logger("completeInterfaceConfig").warn("接口请求记录补全应用授权接口，应用id：{}，服务{}接口{}请求方式{}未匹配到接口，数据周期：{}-{}"
                            , key, applicationName, apiPath, apiMethod, beginTime, endTime);
                    WxRobotOkHttpUtils.post(SafeCenterRobotConfig.AUTH_ALERT, String.format("应用补全 接口请求记录补全应用授权接口，应用id：%s，服务%s接口%s请求方式%s未匹配到接口，数据周期：%s-%s"
                            , key, applicationName, apiPath, apiMethod, beginTime, endTime));
                    return;
                }

                if (!authInterfaceIdList.contains(matchInterface.getId())) {
                    addAuthInterfaceIdSet.add(matchInterface.getId());
                }
            });

            if (!addAuthInterfaceIdSet.isEmpty()) {
                SafeAppAuthInterfaceUpReq authInterfaceUpReq = new SafeAppAuthInterfaceUpReq();
                authInterfaceUpReq.setAppKey(key);
                authInterfaceUpReq.setAddInterfaceIds(addAuthInterfaceIdSet);
                addAppAuthInterfaceList.add(authInterfaceUpReq);
            }
        });
    }

    /**
     * 补全接口配置
     *
     * @param applicationName       服务名
     * @param apiCallStatisticsList 接口请求统计记录
     * @param interfaceList         接口列表
     * @param beginTime             统计开始时间
     * @param endTime               统计结束时间
     * @return 需要更新的接口集合
     */
    private List<SafeInterfaceUpReq> completeInterfaceConfig(String applicationName, List<SafeApiCallStatisticsBO> apiCallStatisticsList, List<SafeInterfaceResp> interfaceList
            , String beginTime, String endTime) {
        // 需要更新的接口
        List<SafeInterfaceUpReq> upInterfaceList = new ArrayList<>();
        // 按照接口唯一标识进行分组
        Map<String, List<SafeApiCallStatisticsBO>> groupedData = apiCallStatisticsList.stream()
                .collect(Collectors.groupingBy(stat -> stat.getApiPath() + "|" + stat.getApiMethod()
                ));
        // 匹配接口
        groupedData.forEach((key, value) -> {
            String apiMethod = value.get(0).getApiMethod();
            String apiPath = value.get(0).getApiPath();
            SafeInterfaceResp matchInterface = InterfaceMatcherUtil.matchApi(interfaceList, apiPath, apiMethod);
            if (matchInterface == null) {
                ExLogger.logger("completeInterfaceConfig").warn("接口请求记录{}接口{}请求方式{}未匹配到接口，数据周期{}-{}：", applicationName, apiPath, apiMethod, beginTime, endTime);
                WxRobotOkHttpUtils.post(SafeCenterRobotConfig.AUTH_ALERT, String.format("接口补全 接口请求记录%s接口%s请求方式%s未匹配到接口，数据周期%s-%s：", applicationName, apiPath, apiMethod, beginTime, endTime));
                return;
            }

            SafeInterfaceUpReq interfaceUpReq = null;
            // 处理网关鉴权模式
            List<AuthInfoDTO> statistidsAuthInfoDTOList = new ArrayList<>(value.stream().map(m -> {
                        AuthGatewayEnum authGatewayEnum = AuthGatewayEnum.getByName(m.getGateway());
                        AuthModeEnum authModeEnum = AuthModeEnum.getByName(m.getAuthMode());
                        if (authGatewayEnum == null || authModeEnum == null) {
                            return null;
                        }
                        AuthInfoDTO tempAuthDTO = new AuthInfoDTO();
                        tempAuthDTO.setAuthGateway(authGatewayEnum);
                        tempAuthDTO.setAuthMode(authModeEnum);
                        return tempAuthDTO;
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            // 按照网关+鉴权模式去重
                            dto -> dto.getAuthGateway().getName() + "-" + dto.getAuthMode().name(), // 组合唯一键
                            dto -> dto, // 取原对象
                            (existing, replacement) -> existing // 发现重复时保留原对象
                    )).values());

            Map<String, List<AuthInfoDTO>> groupGatewayMap = statistidsAuthInfoDTOList.stream().collect(Collectors.groupingBy(dto -> dto.getAuthGateway().getName()));
            for (String authGateway : groupGatewayMap.keySet()) {
                if (groupGatewayMap.get(authGateway).size() > 1) {
                    ExLogger.logger("completeInterfaceConfig").warn("【重要】{}的接口{}请求方式{}同一网关存在多种鉴权模式，数据范围开始{}结束{}", applicationName, apiPath, apiMethod, beginTime, endTime);
                    WxRobotOkHttpUtils.post(SafeCenterRobotConfig.AUTH_ALERT, String.format("接口补全 【重要】%s的接口%s请求方式%s同一网关存在多种鉴权模式，数据范围开始%s结束%s："
                            , applicationName, apiPath, apiMethod, beginTime, endTime));
                    // 结束该接口补全写入
                    return;
                }
            }

            if (StringUtils.isEmpty(matchInterface.getAuthInfo())) {
                interfaceUpReq = new SafeInterfaceUpReq();
                interfaceUpReq.setAuthInfo(statistidsAuthInfoDTOList);
            } else {
                List<AuthInfoDTO> oldAuthInfoDTOList = JSON.parseArray(matchInterface.getAuthInfo(), AuthInfoDTO.class);
                Map<String, List<AuthInfoDTO>> oldAuthInfoMap = oldAuthInfoDTOList.stream().collect(Collectors.groupingBy(m -> m.getAuthGateway().getName()));
                List<AuthInfoDTO> addAuthInfoDTOList = new ArrayList<>();
                for (AuthInfoDTO statistidsAuthInfoDTO : statistidsAuthInfoDTOList) {
                    List<AuthInfoDTO> tempOldAuthInfoDTOList = oldAuthInfoMap.get(statistidsAuthInfoDTO.getAuthGateway().getName());
                    if (CollectionUtils.isEmpty(tempOldAuthInfoDTOList)) {
                        // 没有该网关新增一条数据
                        addAuthInfoDTOList.add(statistidsAuthInfoDTO);
                    } else {
                        AuthInfoDTO oldAuthDTO = tempOldAuthInfoDTOList.get(0);
                        if (oldAuthDTO.getAuthMode() == null) {
                            // 没有鉴权模式设置鉴权模式
                            oldAuthDTO.setAuthMode(statistidsAuthInfoDTO.getAuthMode());
                        } else if (!oldAuthDTO.getAuthMode().equals(statistidsAuthInfoDTO.getAuthMode())) {
                            ExLogger.logger("completeInterfaceConfig").warn("【重要】{}的接口{}请求方式{}，网关{}数据库中鉴权模式{}与记录{}不同，数据范围开始{}结束{}"
                                    , applicationName, apiPath, apiMethod, oldAuthDTO.getAuthGateway().name(), oldAuthDTO.getAuthMode().name(), statistidsAuthInfoDTO.getAuthMode().name(), beginTime, endTime);
                            WxRobotOkHttpUtils.post(SafeCenterRobotConfig.AUTH_ALERT, String.format("接口补全 【重要】%s的接口%s请求方式%s，网关%s数据库中鉴权模式%s与记录%s不同，数据范围开始%s结束%s"
                                    , applicationName, apiPath, apiMethod, oldAuthDTO.getAuthGateway().name(), oldAuthDTO.getAuthMode().name(), statistidsAuthInfoDTO.getAuthMode().name(), beginTime, endTime));
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(addAuthInfoDTOList)) {
                    interfaceUpReq = new SafeInterfaceUpReq();
                    oldAuthInfoDTOList.addAll(addAuthInfoDTOList);
                    interfaceUpReq.setAuthInfo(oldAuthInfoDTOList);
                }
            }

            if (interfaceUpReq != null) {
                interfaceUpReq.setId(matchInterface.getId());
                upInterfaceList.add(interfaceUpReq);
            }
        });

        return upInterfaceList;
    }

}
