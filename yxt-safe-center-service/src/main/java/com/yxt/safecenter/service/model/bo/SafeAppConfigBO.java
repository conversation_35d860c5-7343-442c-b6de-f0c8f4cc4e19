package com.yxt.safecenter.service.model.bo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 应用配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Data
public class SafeAppConfigBO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private Long id;

    /**
     * 应用id
     */
    private String appKey;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 使用非对称方式鉴权密钥、TOKEN等
     */
    private String cerKey;

    /**
     * 鉴权方式 RSA-rsa/rsa2验签 MD5-md5验签 TOKENBS-验证token是否相同的特殊方式
     */
    private String authMethod;

    /**
     * 生效时间
     */
    private LocalDateTime startTime;

    /**
     * 失效时间
     */
    private LocalDateTime endTime;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 数据版本，每次update+1
     */
    private Long version;
}
