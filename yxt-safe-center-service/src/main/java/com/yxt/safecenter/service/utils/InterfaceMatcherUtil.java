package com.yxt.safecenter.service.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.AntPathMatcher;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Since: 2025/2/27 17:49
 * Author: qs
 */
public class InterfaceMatcherUtil {

    private static final AntPathMatcher PATH_MATCHER = new AntPathMatcher();

    public static SafeInterfaceResp matchApi(List<SafeInterfaceResp> interfaceList, String reqPath, String reqMethod) {
        // 统一路径格式，去除前导 /
        String normalizePath = normalizePath(reqPath);
        // 1. 优先匹配静态路径（直接精确匹配）
        List<SafeInterfaceResp> staticMatches = interfaceList.stream()
                .filter(api -> matchMethod(api.getApiWay(), reqMethod))
                .filter(api -> matchExactPath(api.getApiPath(), normalizePath))
                .collect(Collectors.toList());

        if (!staticMatches.isEmpty()) {
            // 同一优先级下，根据请求方式支持数量（越少越精准）排序
            return staticMatches.stream()
                    .min(Comparator.comparingInt(api -> getMethodPrecision(api.getApiWay())))
                    .get();
        }

        // 2. 如果没有匹配到静态路径，再匹配动态路径（使用AntPathMatcher）
        List<SafeInterfaceResp> dynamicMatches = interfaceList.stream()
                .filter(api -> matchMethod(api.getApiWay(), reqMethod))
                .filter(api -> matchPatternPath(api.getApiPath(), normalizePath))
                .collect(Collectors.toList());

        // 获取最小路径的优先级
        OptionalInt minPriority = dynamicMatches.stream()
                .mapToInt(api -> getPathPriority(api.getApiPath(), normalizePath))
                .min();
        // 仅保留优先级得分最小的接口
        List<SafeInterfaceResp> bestMatches = dynamicMatches.stream()
                .filter(api -> getPathPriority(api.getApiPath(), normalizePath) == minPriority.orElse(Integer.MAX_VALUE))
                .collect(Collectors.toList());

        if (!bestMatches.isEmpty()) {
            return bestMatches.stream()
                    .min(Comparator.comparingInt(api -> getMethodPrecision(api.getApiWay())))
                    .get();
        }

        return null;
    }

    /**
     * 判断接口的请求方式是否包含目标请求方法
     */
    private static boolean matchMethod(String apiWayJson, String requestMethod) {
        try {
            List<String> methods = JSON.parseArray(apiWayJson, String.class);
            return methods.contains(requestMethod.toUpperCase());
        } catch (JSONException e) {
            return false;
        }
    }

    /**
     * 精确匹配路径（静态路径），直接判断请求路径是否在列表中
     */
    private static boolean matchExactPath(String apiPathJson, String requestPath) {
        try {
            List<String> normalizedPaths = JSON.parseArray(apiPathJson, String.class)
                    .stream()
                    .map(InterfaceMatcherUtil::normalizePath) // 统一处理缓存路径
                    .collect(Collectors.toList());
            return normalizedPaths.contains(requestPath);
        } catch (JSONException e) {
            return false;
        }
    }

    /**
     * 统一路径格式，去除前导 /
     */
    private static String normalizePath(String path) {
        if (path == null) {
            return "";
        }
        return path.startsWith("/") ? path.substring(1) : path;
    }

    /**
     * 动态路径匹配（使用AntPathMatcher），判断是否有匹配
     */
    private static boolean matchPatternPath(String apiPathJson, String requestPath) {
        try {
            List<String> paths = JSON.parseArray(apiPathJson, String.class);
            return paths.stream()
                    .map(InterfaceMatcherUtil::normalizePath) // 统一处理缓存路径
                    .anyMatch(path -> PATH_MATCHER.match(path, requestPath));
        } catch (JSONException e) {
            return false;
        }
    }

    /**
     * 获取请求方式匹配的精准度，返回值越小表示越精准
     * 例如：["GET"] 返回 1，比 ["GET","POST"] 的返回 2 更精准
     */
    private static int getMethodPrecision(String apiWayJson) {
        try {
            List<String> methods = JSON.parseArray(apiWayJson, String.class);
            return methods.size();
        } catch (JSONException e) {
            return Integer.MAX_VALUE;
        }
    }

    /**
     * 计算路径优先级，数值越小优先级越高：
     * - 静态路径优先级最高（但静态匹配已在上层处理）
     * - 具体的路径参数 `{xxx}` 比 `*` 优先
     * - `api/{id}` (优先) > `api/*` (次之)
     */
    private static int getPathPriority(String apiPathJson, String reqPath) {
        try {
            List<String> normalizedPaths = JSON.parseArray(apiPathJson, String.class)
                    .stream()
                    .map(InterfaceMatcherUtil::normalizePath) // 统一处理缓存路径
                    .collect(Collectors.toList());
            return normalizedPaths.stream()
                    .filter(path -> PATH_MATCHER.match(path, reqPath))
                    .mapToInt(InterfaceMatcherUtil::calculatePathPriority)
                    .min()
                    .orElse(Integer.MAX_VALUE);
        } catch (JSONException e) {
            return Integer.MAX_VALUE;
        }
    }

    /**
     * 计算单个路径的优先级：
     */
    private static int calculatePathPriority(String path) {
        if (path == null) {
            return Integer.MAX_VALUE;
        }
        String[] pathArr = path.split("/");
        int score = 0;
        for (String pathSplit : pathArr) {
            if (StringUtils.isEmpty(pathSplit)) {
                continue;
            }
            if (pathSplit.contains("*")) {
                score += 100;
            } else if (pathSplit.contains("{")) {
                score += 50;
            } else {
                score += 1;
            }
        }

        return score;
    }

    public static void main(String[] args) {
//        List<InterfaceInfoDTO> interfaceInfoList = new ArrayList<>();
//        InterfaceInfoDTO interfaceInfoDTO1 = new InterfaceInfoDTO();
//        InterfaceInfoDTO interfaceInfoDTO2 = new InterfaceInfoDTO();
//        interfaceInfoList.add(interfaceInfoDTO1);
//        interfaceInfoList.add(interfaceInfoDTO2);
//        interfaceInfoDTO1.setId(1l);
//        interfaceInfoDTO1.setApiWay("[\"GET\",\"POST\"]");
//        interfaceInfoDTO1.setApiPath("[\"/api/{oid}\"]");
//        interfaceInfoDTO2.setId(2l);
//        interfaceInfoDTO2.setApiWay("[\"GET\"]");
//        interfaceInfoDTO2.setApiPath("[\"/api/*\",\"/api/{oid}\"]");
//        InterfaceInfoDTO matchInterface = matchApi(interfaceInfoList, "api/ttt", "GET");
//        System.out.println("匹配到接口：" + matchInterface);

        List<SafeInterfaceResp> interfaceInfoList = new ArrayList<>();
        SafeInterfaceResp safeInterfaceResp1 = new SafeInterfaceResp();
        SafeInterfaceResp safeInterfaceResp2 = new SafeInterfaceResp();
        interfaceInfoList.add(safeInterfaceResp1);
        interfaceInfoList.add(safeInterfaceResp2);
        safeInterfaceResp1.setId(1l);
        safeInterfaceResp1.setApiWay("[\"GET\"]");
        safeInterfaceResp1.setApiPath("\"[/*]\"");
        safeInterfaceResp2.setId(2l);
        safeInterfaceResp2.setApiWay("[\"GET\"]");
        safeInterfaceResp2.setApiPath("[\"/api/*\"]");
        SafeInterfaceResp matchedInterface = matchApi(interfaceInfoList, "/api/test", "GET");
        System.out.println("匹配到接口：" + matchedInterface);
    }
}
