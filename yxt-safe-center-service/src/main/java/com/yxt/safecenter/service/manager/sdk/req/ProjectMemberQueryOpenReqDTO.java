package com.yxt.safecenter.service.manager.sdk.req;

import com.yxt.lang.dto.api.RequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Since: 2025/07/29 17:36
 * Author: qs
 */

@ApiModel("根据成员查询项目请求参数")
@Data
public class ProjectMemberQueryOpenReqDTO implements Serializable {
    private static final long serialVersionUID = -5813438865424288064L;
    @ApiModelProperty("开发人员编号")
    private List<String> developerNoList;
    @ApiModelProperty("产品人员编号")
    private List<String> productManagerNoList;
    @ApiModelProperty("测试人员编号")
    private List<String> testerNoList;
    @ApiModelProperty("项目负责人编号")
    private List<String> projectLeaderNoList;
    @ApiModelProperty("指定范围AppKey")
    private List<String> appKeyList;
}
