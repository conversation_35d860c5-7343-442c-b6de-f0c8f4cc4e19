package com.yxt.safecenter.service.manager.iface;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.safecenter.service.model.bo.SafeAppAuthInterfaceBO;
import com.yxt.safecenter.service.model.bo.SafeInterfaceBO;
import com.yxt.safecenter.common.model.dto.req.SafeAppAuthInterfaceQueryReq;
import com.yxt.safecenter.service.model.redis.BizRedisCacheGroup;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 应用授权接口表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface SafeAppAuthInterfaceManager {

    /**
     * 分页查询
     *
     * @param authInterfaceQueryReq
     * @return
     */
    PageDTO<SafeAppAuthInterfaceBO> page(SafeAppAuthInterfaceQueryReq authInterfaceQueryReq);

    /**
     * 查询基本信息
     *
     * @param appKey
     * @param applicationName
     * @return list
     */
    List<SafeAppAuthInterfaceBO> listByAppKeyAndApplicationName(String appKey, String applicationName);

    /**
     * 查询基本信息
     *
     * @param appKey
     * @return
     */
    @Cacheable(cacheNames = BizRedisCacheGroup.SAFE_INTER_FACE_AUTH_APP_KEY, key = "#appKey")
    List<SafeAppAuthInterfaceBO> listByAppKey(String appKey);

    /**
     * 取消授权
     *
     * @param appKey
     * @param interfaceIds
     */
    @CacheEvict(cacheNames = BizRedisCacheGroup.SAFE_INTER_FACE_AUTH_APP_KEY, key = "#appKey")
    void delByAppKeyAndInterfaceIds(String appKey, Set<Long> interfaceIds);

    /**
     * 取消授权接口
     * @param appKey
     */
    @CacheEvict(cacheNames = BizRedisCacheGroup.SAFE_INTER_FACE_AUTH_APP_KEY, key = "#appKey")
    void delByAppKey(String appKey);
    /**
     * 新增授权接口
     *
     * @param appKey
     * @param safeInterfaceBOS
     */
    @CacheEvict(cacheNames = BizRedisCacheGroup.SAFE_INTER_FACE_AUTH_APP_KEY, key = "#appKey")
    void saveBatch(String appKey, List<SafeInterfaceBO> safeInterfaceBOS);
}
