package com.yxt.safecenter.service.manager.sdk;


import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.model.constant.CommonConstant;
import com.yxt.safecenter.service.manager.sdk.req.ProjectMemberQueryOpenReqDTO;
import com.yxt.safecenter.service.manager.sdk.resp.ProjectMemberInfoResDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 项目管理开放接口
 * Since: 2025/07/29 17:33
 * Author: qs
 */
@FeignClient(value = CommonConstant.SERVICE_NAME_BASIS)
public interface ProjectManagementQueryOpenApi {

    /**
     * 根据人员信息查询所属项目
     */
    @PostMapping({"/openSdk/projectManagement/r/1.0/listByMember"})
    ResponseBase<List<ProjectMemberInfoResDTO>> listProjectByMember(@RequestBody ProjectMemberQueryOpenReqDTO memberQueryReqDTO);

}
