package com.yxt.safecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.service.UserProjectService;
import com.yxt.safecenter.service.manager.sdk.ProjectManagementQueryOpenApi;
import com.yxt.safecenter.service.manager.sdk.req.ProjectMemberQueryOpenReqDTO;
import com.yxt.safecenter.service.manager.sdk.resp.ProjectMemberInfoResDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Since: 2025/07/29 10:00
 * Author: qs
 */

@Service
public class UserProjectServiceImpl implements UserProjectService {

    @Resource
    private ProjectManagementQueryOpenApi projectManagementQueryOpenApi;

    @Override
    public Set<String> getUserApplicationNameSet(String empCode, String applicationName) {
        // 权限
        ProjectMemberQueryOpenReqDTO memberQueryOpenReqDTO = new ProjectMemberQueryOpenReqDTO();
        memberQueryOpenReqDTO.setDeveloperNoList(Collections.singletonList(empCode));
        if (StringUtils.isNotEmpty(applicationName)) {
            memberQueryOpenReqDTO.setAppKeyList(Collections.singletonList(applicationName));
        }
        ResponseBase<List<ProjectMemberInfoResDTO>> responseBase = projectManagementQueryOpenApi.listProjectByMember(memberQueryOpenReqDTO);
        ExLogger.logger().info("获取项目权限req：{}，resp：{}", JSON.toJSONString(memberQueryOpenReqDTO), responseBase);
        if (!responseBase.checkSuccess()) {
            throw new YxtBizException("获取人员项目权限失败：" + responseBase.getMsg());
        }
        List<ProjectMemberInfoResDTO> projectDataList = responseBase.getData();
        if (CollectionUtils.isNotEmpty(projectDataList)) {
            // 加分页参数
            return projectDataList.stream().map(ProjectMemberInfoResDTO::getAppKey).collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }
}
