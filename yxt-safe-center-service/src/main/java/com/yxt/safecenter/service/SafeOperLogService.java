package com.yxt.safecenter.service;


import com.yxt.lang.dto.api.PageDTO;
import com.yxt.safecenter.common.framework.log.service.OperLogService;
import com.yxt.safecenter.common.model.dto.req.SafeOperLogQueryReq;
import com.yxt.safecenter.common.model.dto.resp.SafeOperLogResp;

/**
 * 系统接口操作日志
 *
 * <AUTHOR>
 * @date 2025年02月18日 15:18
 */
public interface SafeOperLogService extends OperLogService {


    /**
     * 清空操作记录
     */
    void cleanOperLog();


    /**
     * 分页查询操作记录
     *
     * @param queryReq
     * @return
     */
    PageDTO<SafeOperLogResp> page(SafeOperLogQueryReq queryReq);
}
