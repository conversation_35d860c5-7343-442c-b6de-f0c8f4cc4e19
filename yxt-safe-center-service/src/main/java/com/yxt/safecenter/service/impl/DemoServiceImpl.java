package com.yxt.safecenter.service.impl;

import com.yxt.safecenter.service.DemoService;
import com.yxt.safecenter.service.model.bo.DemoBO;
import com.yxt.safecenter.service.manager.iface.DemoManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class DemoServiceImpl implements DemoService {
    @Resource
    private DemoManager manager;


    @Override
    public DemoBO selectDictById(Integer id) {
        return manager.selectDictById(id);
    }

    @Override
    public DemoBO selectDictByIdFromRedis(Integer diseaseDictId) {
        return manager.selectDictByIdFromRedis(diseaseDictId);
    }
}
