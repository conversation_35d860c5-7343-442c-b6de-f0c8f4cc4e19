package com.yxt.safecenter.service;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.safecenter.common.model.bo.SafeAppConfigBaseUpBO;
import com.yxt.safecenter.common.model.bo.SafeAppConfigUpOrAddBO;
import com.yxt.safecenter.common.model.dto.req.SafeAppAuthInterfaceUpReq;
import com.yxt.safecenter.common.model.dto.req.SafeAppAuthInterfaceQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeAppConfigQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeEnableAppConfigInterfacePageReq;
import com.yxt.safecenter.service.model.bo.SafeAppConfigEBO;
import com.yxt.safecenter.common.model.dto.resp.SafeAppConfigInterfaceApiResp;
import com.yxt.safecenter.common.model.dto.resp.SafeAppConfigResp;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;


import java.util.Collection;
import java.util.List;

/**
 * 应用管理
 *
 * <AUTHOR>
 * @date 2025年02月08日 17:08
 */
public interface SafeAppConfigService {

    /**
     * 分页查询
     *
     * @param queryReq
     * @return
     */
    PageDTO<SafeAppConfigResp> page(SafeAppConfigQueryReq queryReq);

    /**
     * 获取应用配置信息
     *
     * @param appKey
     * @return
     */
    SafeAppConfigResp getByAppKey(String appKey);

    /**
     * 获取应用配置信息
     * @param appKeys appKey集合
     * @return list
     */
    List<SafeAppConfigResp> listByAppKeys(Collection<String> appKeys);

    /**
     * 新增/更新
     *
     * @param upOrAddReq
     */
    void saveOrUp(SafeAppConfigUpOrAddBO upOrAddReq);

    /**
     * 更新应用信息
     *
     * @param upReq
     */
    void updateBase(SafeAppConfigBaseUpBO upReq);

    /**
     * 删除应用信息
     *
     * @param id
     */
    void del(Long id);

    /**
     * 授权的接口信息_查询
     *
     * @param authInterfaceQueryReq
     * @return
     */
    PageDTO<SafeInterfaceResp> authInterfacePage(SafeAppAuthInterfaceQueryReq authInterfaceQueryReq);

    /**
     * 授权的接口信息_查询
     * @param appKey 应用id
     * @param applicationName 应用名称
     * @return list
     */
    List<Long> authInterfaceIdList(String appKey, String applicationName);

    /**
     * 授权的接口信息_添加
     * @param upReq
     */
    void authInterfaceUp(SafeAppAuthInterfaceUpReq upReq);

    /**
     * 分页查询可用的应用配置 + 授权接口
     *
     * @param pageReq 查询参数
     * @return pageDTO
     */
    PageDTO<SafeAppConfigInterfaceApiResp> enableAppConfigInterfacePage(SafeEnableAppConfigInterfacePageReq pageReq);

    /**
     * Excel导入应用配置
     *
     * @param imports Excel导入数据列表
     */
    void imports(List<SafeAppConfigEBO> imports);

}
