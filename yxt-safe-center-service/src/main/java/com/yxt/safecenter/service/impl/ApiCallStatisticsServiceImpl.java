package com.yxt.safecenter.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.util.MD5Util;
import com.yxt.safecenter.common.lib.util.LocalDateUtils;
import com.yxt.safecenter.common.model.dto.req.ApiCallStatisticsReq;
import com.yxt.safecenter.common.model.dto.req.SafeApiCallStatisticsPageReq;
import com.yxt.safecenter.common.model.dto.resp.SafeApiCallStatisticsResp;
import com.yxt.safecenter.service.ApiCallStatisticsService;
import com.yxt.safecenter.service.manager.iface.SafeApiCallStatisticsManager;
import com.yxt.safecenter.service.model.bo.SafeApiCallStatisticsBO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * Since: 2025/2/14 16:09
 * Author: qs
 */

@Service
public class ApiCallStatisticsServiceImpl implements ApiCallStatisticsService {

    @Resource
    private SafeApiCallStatisticsManager safeApiCallStatisticsManager;

    @Override
    public void apiStatisticsSaveBatch(List<ApiCallStatisticsReq> reqList) {
        List<SafeApiCallStatisticsBO> boList = BeanUtil.copyToList(reqList, SafeApiCallStatisticsBO.class);
        boList.forEach(bo -> {
            bo.setUniKey(MD5Util.MD5Encode(bo.getPeriod() + bo.getApplicationName() + bo.getApiPath() + bo.getApiMethod() + bo.getGateway() + bo.getAppKey() + bo.getAuthMode() + bo.getAppAuthMethod()
                    , StandardCharsets.UTF_8.name()));
        });
        safeApiCallStatisticsManager.batchUpsert(boList);
    }

    @Override
    public List<SafeApiCallStatisticsBO> listByApplicationNameAndTime(String applicationName, String beginTime, String endTime) {
        return safeApiCallStatisticsManager.listByApplicationNameAndTime(applicationName, beginTime, endTime);
    }

    @Override
    public PageDTO<SafeApiCallStatisticsResp> page(SafeApiCallStatisticsPageReq pageReq) {
        PageDTO<SafeApiCallStatisticsResp> pageResp = safeApiCallStatisticsManager.page(pageReq);
        List<SafeApiCallStatisticsResp> dataList = safeApiCallStatisticsManager.page(pageReq).getData();
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(data -> {
                data.setTimePeriod(LocalDateUtils.localDateTime2String(data.getStartTime()) + "~" + LocalDateUtils.localDateTime2String(data.getEndTime()));
            });
        }
        pageResp.setData(dataList);
        return pageResp;
    }

    @Override
    public void clearOldData(LocalDateTime thresholdDate) {
        if (thresholdDate == null) {
            return;
        }
        safeApiCallStatisticsManager.clearOldData(thresholdDate);
    }
}
