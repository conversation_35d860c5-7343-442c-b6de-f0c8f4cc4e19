package com.yxt.safecenter.service.model.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SafeInterfaceEBO {

    @ExcelProperty("应用名称")
    private String applicationName;

    @ExcelProperty("API 类")
    private String apiClass;

    @ExcelProperty("API 方法")
    private String apiMethod;

    @ExcelProperty("API 请求方式")
    private String apiWay;

    @ExcelProperty("API 路径")
    private String apiPath;

    @ExcelProperty("API 描述")
    private String apiDesc;

    /**
     * 权限相关 json
     */
    @ExcelProperty("权限信息")
    private String authInfo;

    @ExcelProperty("加解密")
    private String encryptionRequired;

    @ExcelProperty(value = "状态 上线中-ONLINE_RUNNING，已上线-ONLINE，下线中-OFFLINE_RUNNING，已下线-OFFLINE")
    private String status;
}