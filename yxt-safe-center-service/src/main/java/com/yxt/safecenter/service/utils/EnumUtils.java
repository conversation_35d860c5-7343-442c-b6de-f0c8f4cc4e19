package com.yxt.safecenter.service.utils;

import cn.hutool.core.util.ClassUtil;
import com.yxt.safecenter.common.model.dto.resp.MetaKeyValueResp;
import com.yxt.safecenter.common.model.enums.BaseEnum;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class EnumUtils {

    private static final Map<String, Class<? extends BaseEnum>> enumCache = new ConcurrentHashMap<>();

    static {
        // 延迟加载枚举类
        loadEnums();
    }

    private static void loadEnums() {
        Set<Class<?>> classes = ClassUtil.scanPackage("com.yxt.safecenter");
        Set<Class<? extends BaseEnum>> enumSet = classes.stream()
                .filter(ClassUtil::isEnum)
                .filter(cls -> BaseEnum.class.isAssignableFrom(cls))
                .map(cls -> (Class<? extends BaseEnum>) cls)
                .collect(Collectors.toSet());

        for (Class<? extends BaseEnum> enumClass : enumSet) {
            enumCache.put(enumClass.getSimpleName().replace("Enum", "").toLowerCase(), enumClass);
        }
    }


    /**
     * 根据传入的字符串获取对应的枚举类的所有枚举实例
     *
     * @param enumName 枚举类的名称（如 "ColorEnum" 或 "color"）
     * @return 枚举实例列表
     */
    public static List<? extends BaseEnum> getEnumByEnumName(String enumName) {
        Class<? extends BaseEnum> enumClass = enumCache.get(enumName.toLowerCase().replace("enum", ""));

        if (enumClass == null) {
            throw new IllegalArgumentException("Enum class not found: " + enumName);
        }

        return Arrays.stream(enumClass.getEnumConstants()).collect(Collectors.toList());
    }

    /**
     * 根据传入的字符串获取对应的枚举类的所有枚举实例
     *
     * @param enumName 枚举类的名称（如 "ColorEnum" 或 "color"）
     * @return 枚举实例name code
     */
    public static List<MetaKeyValueResp<String, Object, String>> getByEnumName(String enumName) {
        return getEnumByEnumName(enumName).stream()
                .map(t -> new MetaKeyValueResp<>(t.getName(), t.getCode(), t.getDesc())).collect(Collectors.toList());
    }

    /**
     * 所有枚举实例信息获取
     */
    public static Map<String, List<MetaKeyValueResp<String, Object, String>>> getAllEnums() {
        Map<String, List<MetaKeyValueResp<String, Object, String>>> map = new HashMap<>();
        for (Map.Entry<String, Class<? extends BaseEnum>> entry : enumCache.entrySet()) {
            map.put(entry.getKey(), Arrays.stream(entry.getValue().getEnumConstants())
                    .map(t -> new MetaKeyValueResp<>(t.getName(), t.getCode(), t.getDesc())).collect(Collectors.toList()));
        }
        return map;
    }

    public static void main(String[] args) {
        List<? extends BaseEnum> apiWayEnums = getEnumByEnumName("AuthGatewayEnum");
        for (BaseEnum apiWayEnum : apiWayEnums) {
            System.out.println(apiWayEnum.getName() + "_" + apiWayEnum.getCode());
        }
        Map<String, List<MetaKeyValueResp<String, Object, String>>> allEnums = getAllEnums();
        System.out.println(allEnums);
    }
}
