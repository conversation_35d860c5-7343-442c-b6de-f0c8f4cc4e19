package com.yxt.safecenter.service.utils.excel.convert;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.springframework.util.StringUtils;

import java.time.LocalDate;

/**
 * Since: 2025/2/26 14:24
 * Author: qs
 */

public class EasyExcelLocalDateConverter implements Converter<LocalDate> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return LocalDate.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public LocalDate convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if(null==cellData||!StringUtils.hasLength(cellData.getStringValue())) {
            return null;
        }
        return LocalDateTimeUtil.parseDate(cellData.getStringValue());
    }

    @Override
    public WriteCellData<LocalDate> convertToExcelData(LocalDate value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if(null==value) {
            return new WriteCellData<>();
        }
        return new WriteCellData<>(LocalDateTimeUtil.formatNormal(value));
    }
}

