package com.yxt.safecenter.service.manager.iface;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.safecenter.common.model.dto.req.SafeApiCallStatisticsPageReq;
import com.yxt.safecenter.common.model.dto.resp.SafeApiCallStatisticsResp;
import com.yxt.safecenter.service.model.bo.SafeApiCallStatisticsBO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 接口数据统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface SafeApiCallStatisticsManager {

    /**
     * 批量插入/更新 按照唯一索引判断更新
     * @param records 请求数据
     */
    void batchUpsert(List<SafeApiCallStatisticsBO> records);

    /**
     * 按应用名称和日期范围查询
     * @param applicationName 应用名
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return list
     */
    List<SafeApiCallStatisticsBO> listByApplicationNameAndTime(String applicationName, String beginTime, String endTime);

    /**
     * 分页查询
     *
     * @param pageReq 请求参数
     * @return PageDTO
     */
    PageDTO<SafeApiCallStatisticsResp> page(SafeApiCallStatisticsPageReq pageReq);

    /**
     * 分页查询
     *
     * @param thresholdDate  时间阈值，保留改时间及之后得数据
     */
    void clearOldData(LocalDateTime thresholdDate );
}
