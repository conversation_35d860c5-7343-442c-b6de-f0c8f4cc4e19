package com.yxt.safecenter.service.model.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 应用授权接口表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@Data
@ApiModel(description = "应用授权接口表")
public class SafeAppAuthInterfaceBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键", required = true, example = "1")
    private Long id;

    @ApiModelProperty(value = "应用id", required = true, example = "app12345")
    private String appKey;

    @ApiModelProperty(value = "服务名", required = true, example = "MyApplication")
    private String applicationName;

    @ApiModelProperty(value = "创建人", required = true, example = "admin")
    private String createdBy;

    @ApiModelProperty(value = "接口id", required = true, example = "12345")
    private Long interfaceId;

    @ApiModelProperty(value = "创建时间", example = "2025-02-13T10:00:00")
    private LocalDateTime createdTime;
}