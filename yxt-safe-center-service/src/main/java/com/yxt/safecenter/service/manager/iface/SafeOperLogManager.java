package com.yxt.safecenter.service.manager.iface;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.safecenter.common.framework.log.model.bo.SafeOperLogBO;
import com.yxt.safecenter.common.model.dto.req.SafeOperLogQueryReq;
import com.yxt.safecenter.common.model.dto.resp.SafeOperLogResp;

/**
 * .
 *
 * <AUTHOR>
 * @date 2025年02月18日 15:19
 */
public interface SafeOperLogManager {
    /**
     * .
     *
     * @param operLog
     */
    void save(SafeOperLogBO operLog);

    /**
     * .
     */
    void cleanOperLog();

    /**
     * .
     *
     * @param queryReq
     * @return
     */
    PageDTO<SafeOperLogResp> page(SafeOperLogQueryReq queryReq);
}
