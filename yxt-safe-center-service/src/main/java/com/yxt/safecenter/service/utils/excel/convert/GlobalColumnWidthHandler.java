package com.yxt.safecenter.service.utils.excel.convert;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;

import org.apache.poi.ss.usermodel.Sheet;

import java.lang.reflect.Field;
import java.util.Arrays;

public class GlobalColumnWidthHandler implements SheetWriteHandler {

    private final int defaultColumnWidth;
    private final Class<?> clazz;

    public GlobalColumnWidthHandler(int defaultColumnWidth, Class<?> clazz) {
        this.defaultColumnWidth = defaultColumnWidth;
        this.clazz = clazz;
    }

    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context) {
        // 获取 Sheet 对象
        Sheet sheet = context.getWriteSheetHolder().getSheet();

        // 动态获取列数（基于实体类字段数量）
        int lastColumnIndex = getColumnCount(clazz);

        // 设置默认列宽
        for (int i = 0; i < lastColumnIndex; i++) {
            sheet.setColumnWidth(i, defaultColumnWidth * 256);
        }
    }

    /**
     * 获取实体类字段数量
     */
    private int getColumnCount(Class<?> clazz) {
        return Arrays.stream(clazz.getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(com.alibaba.excel.annotation.ExcelProperty.class))
                .toArray().length;
    }
}