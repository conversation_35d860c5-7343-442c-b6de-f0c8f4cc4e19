package com.yxt.safecenter.service.utils;

import com.yxt.lang.dto.PageBase;
import com.yxt.lang.dto.api.PageDTO;
import org.apache.poi.ss.formula.functions.T;

import java.util.Collections;
import java.util.List;

public class PageDtoUtil {


    /**
     * 设置 datas
     *
     * @param iPage
     * @param datas
     * @param <T>
     * @return
     */
    public static <T> PageDTO<T> convert(PageDTO<?> iPage, List<T> datas) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setTotalCount(iPage.getTotalCount());
        pageDTO.setTotalPage(iPage.getTotalPage());
        pageDTO.setData(datas);
        return pageDTO;
    }

    /**
     * 空的分页对象
     * @param pageBase 分页基类
     */
    public static PageDTO emptyPageDTO(PageBase pageBase) {
        PageDTO<T> pageDTO = new PageDTO<>(pageBase.getCurrentPage(), pageBase.getPageSize());
        pageDTO.setTotalCount(0L);
        pageDTO.setTotalPage(0L);
        pageDTO.setData(Collections.emptyList());
        return pageDTO;
    }
}
