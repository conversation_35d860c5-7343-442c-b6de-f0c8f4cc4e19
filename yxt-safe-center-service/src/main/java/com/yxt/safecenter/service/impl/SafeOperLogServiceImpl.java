package com.yxt.safecenter.service.impl;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.safecenter.common.framework.log.model.bo.SafeOperLogBO;
import com.yxt.safecenter.common.model.dto.req.SafeOperLogQueryReq;
import com.yxt.safecenter.common.model.dto.resp.SafeOperLogResp;
import com.yxt.safecenter.service.SafeOperLogService;
import com.yxt.safecenter.service.manager.iface.SafeOperLogManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * .
 *
 * <AUTHOR>
 * @date 2025年02月18日 15:19
 */
@Service
public class SafeOperLogServiceImpl implements SafeOperLogService {

    @Autowired
    private SafeOperLogManager safeOperLogManager;

    @Override
    public void insertOperlog(SafeOperLogBO operLog) {
        safeOperLogManager.save(operLog);
    }

    @Override
    public void cleanOperLog() {
        safeOperLogManager.cleanOperLog();
    }

    @Override
    public PageDTO<SafeOperLogResp> page(SafeOperLogQueryReq queryReq) {
        return safeOperLogManager.page(queryReq);
    }
}
