package com.yxt.safecenter.service.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;

public class JsonUtil {

    /**
     * 判断字符串是否为有效的 JSON
     *
     * @param str
     * @return
     */
    public static boolean isValidJson(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }

        try {
            JSON.parseObject(str);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为有效的 JSON
     *
     * @param str
     * @return
     */
    public static boolean isValidJsonArray(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }

        try {
            JSONArray.parseArray(str);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }

    public static void main(String[] args) {
        System.out.println(isValid<PERSON>son("{\"name\":\"John\",\"age\":30}"));  // 输出 true
        System.out.println(isValid<PERSON>son("{name:<PERSON>, age:30}"));  // 输出 false
        System.out.println(isValidJson("Hello World"));  // 输出 false
        System.out.println(isValidJsonArray("[\"/api/orders\",\"/api/v2/orders\"]"));  // 输出 true
    }
}
