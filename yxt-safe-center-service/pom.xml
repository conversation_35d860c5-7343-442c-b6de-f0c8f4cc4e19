<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-safe-center-service</artifactId>
    <name>yxt-safe-center-service</name>
    <description>yxt-safe-center-service</description>

    <parent>
        <groupId>com.yxt.safecenter</groupId>
        <artifactId>yxt-safe-center</artifactId>
        <version>${reversion}</version>
    </parent>

    <properties>
        <module.deploy.skip>true</module.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.safecenter</groupId>
            <artifactId>yxt-safe-center-common-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-core-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- xxljob 依赖-->
        <!--
                <dependency>
                    <groupId>com.yxt</groupId>
                    <artifactId>yxt-xxljob-spring-boot-starter</artifactId>
                </dependency>
        -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>io.lettuce</groupId>-->
        <!--            <artifactId>lettuce-core</artifactId>-->
        <!--            <scope>provided</scope>-->
        <!--        </dependency>-->
    </dependencies>

</project>
