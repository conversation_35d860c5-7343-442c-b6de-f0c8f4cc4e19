<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.yxt.safecenter</groupId>
    <artifactId>yxt-safe-center</artifactId>
    <version>${reversion}</version>
    <name>yxt-safe-center</name>
    <description>yxt mvc 种子项目</description>
    <packaging>pom</packaging>

    <parent>
        <artifactId>yxt-xframe</artifactId>
        <groupId>com.yxt</groupId>
        <version>2.18.1</version>
    </parent>

    <properties>
        <reversion>1.0.0-SNAPSHOT</reversion>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
        <module.deploy.skip>false</module.deploy.skip>
    </properties>

    <modules>
        <module>yxt-safe-center-sdk</module>
        <module>yxt-safe-center-application</module>
        <module>yxt-safe-center-manager</module>
        <module>yxt-safe-center-infrastructure</module>
        <module>yxt-safe-center-common</module>
        <module>yxt-safe-center-bootstrap</module>
        <module>yxt-safe-center-service</module>
        <module>yxt-safe-center-message-sdk</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>yxt-redis-spring-boot-starter</artifactId>
                <version>4.4.0</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.safecenter</groupId>
                <artifactId>yxt-safe-center-application</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.safecenter</groupId>
                <artifactId>yxt-safe-center-common</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.safecenter</groupId>
                <artifactId>yxt-safe-center-common-model</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.safecenter</groupId>
                <artifactId>yxt-safe-center-common-lib</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.safecenter</groupId>
                <artifactId>yxt-safe-center-common-framework</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.safecenter</groupId>
                <artifactId>yxt-safe-center-service</artifactId>
                <version>${reversion}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>io.github.openfeign</groupId>-->
            <!--                <artifactId>feign-core</artifactId>-->
            <!--                <version>10.1.0</version>-->
            <!--&lt;!&ndash;                <scope>compile</scope>&ndash;&gt;-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.yxt.safecenter</groupId>
                <artifactId>yxt-safe-center-manager</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.safecenter</groupId>
                <artifactId>yxt-safe-center-infrastructure</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.safecenter</groupId>
                <artifactId>yxt-safe-center-sdk</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.safecenter</groupId>
                <artifactId>yxt-safe-center-message-sdk</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.safecenter</groupId>
                <artifactId>yxt-safe-center-online-sdk</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.safecenter</groupId>
                <artifactId>yxt-safe-center-feign-sdk</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>1.21</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>2.12.6</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>5.2.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>5.2.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
