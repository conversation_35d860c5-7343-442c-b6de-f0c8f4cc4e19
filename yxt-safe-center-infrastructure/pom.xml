<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-safe-center-infrastructure</artifactId>
    <name>yxt-safe-center-infrastructure</name>
    <description>yxt-safe-center-infrastructure</description>

    <parent>
        <groupId>com.yxt.safecenter</groupId>
        <artifactId>yxt-safe-center</artifactId>
        <version>${reversion}</version>
    </parent>

    <properties>
        <module.deploy.skip>true</module.deploy.skip>
        <sonar.sources>src/main/java,src/main/resources</sonar.sources>
    </properties>

    <dependencies>
        <!-- orm相关依赖 -->
        <!-- 引入shardingJDBC后，需增加类型转换，顺序要在mybatis之前-->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <!--        todo starter 有问题，加密工具报错，starter里面的Controller注入失败 -->
<!--        <dependency>-->
<!--            <groupId>com.yxt</groupId>-->
<!--            <artifactId>yxt-mybatisplus-spring-boot-starter</artifactId>-->
<!--        </dependency>-->
        <!--数据源相关依赖-->
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <!--数据库相关依赖-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        
        <!-- mongodb组件相关依赖 -->
        <!--
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-mongodb</artifactId>
                </dependency>
        -->

        <!-- elastic组件相关依赖 -->
        <!--
                <dependency>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                </dependency>

                <dependency>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </dependency>
        -->
        <!-- easy-es组件相关依赖 -->

        <!--
                        <dependency>
                            <groupId>org.dromara.easy-es</groupId>
                            <artifactId>easy-es-boot-starter</artifactId>
                        </dependency>
        -->

        <!-- safecenter组件相关依赖 -->
        <dependency>
            <groupId>com.yxt.safecenter</groupId>
            <artifactId>yxt-safe-center-common-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.safecenter</groupId>
            <artifactId>yxt-safe-center-common-lib</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-mvc-dalgenplugin</artifactId>
            <version>1.0.2</version>
        </dependency>
        <!-- common-lang -->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-lang</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>com.yxt</groupId>
                <artifactId>yxt-mvc-dalgenplugin</artifactId>
                <version>1.0.3</version>
                <configuration>
                    <baseDir>${basedir}</baseDir>
                    <projectName>${artifactId}</projectName>
                    <dataSource>
                        <driverName>com.mysql.jdbc.Driver</driverName>
                        <url>*****************************************************</url>
                        <username>agent</username>
                        <password>FgRdxNn8ADFC</password>
                    </dataSource>
                    <strategy>
                        <!--
                          表生成策略：
                            1. 如果填写 property，则只生成填写的表
                            2. 如果不填写 property，则生成所有表
                          建议每次填写需要生成的表，避免生成不必要的表，造成以外
                        -->
<!--                        <include>-->
<!--                            <property>safe_oper_log</property>-->
<!--                        </include>-->
                    </strategy>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>8.0.11</version>
                    </dependency>
                    <dependency>
                        <groupId>org.apache.velocity</groupId>
                        <artifactId>velocity</artifactId>
                        <version>1.7</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>
