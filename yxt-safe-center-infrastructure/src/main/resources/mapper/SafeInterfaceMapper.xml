<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.safecenter.infrastructure.dao.mapper.SafeInterfaceMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into safe_interface (
        id,
        application_name,
        api_class,
        api_method,
        api_way,
        api_path,
        api_desc,
        auth_mode,
        auth_gateway,
        rbac_validation,
        encryption_required,
        status,
        created_by,
        updated_by,
        created_time,
        updated_time,
        version
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.applicationName},
        #{item.apiClass},
        #{item.apiMethod},
        #{item.apiWay},
        #{item.apiPath},
        #{item.apiDesc},
        #{item.authMode},
        #{item.authGateway},
        #{item.rbacValidation},
        #{item.encryptionRequired},
        #{item.status},
        #{item.createdBy},
        #{item.updatedBy},
        #{item.createdTime},
        #{item.updatedTime},
        #{item.version}
        )
        </foreach>
    </insert>

    <update id="updateBaseBatch">
        <foreach collection="records" item="item" index="index" separator=";">
            UPDATE safe_interface
            <set>
                <if test="item.apiDesc != null">
                    api_desc = #{item.apiDesc},
                </if>
                <if test="item.authInfo != null">
                    auth_info = #{item.authInfo},
                </if>
                <if test="item.encryptionRequired != null">
                    encryption_required = #{item.encryptionRequired},
                </if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>
</mapper>
