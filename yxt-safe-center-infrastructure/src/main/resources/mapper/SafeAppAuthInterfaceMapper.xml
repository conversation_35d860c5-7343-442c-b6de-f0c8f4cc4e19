<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.safecenter.infrastructure.dao.mapper.SafeAppAuthInterfaceMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into safe_app_auth_interface (
        id,
        app_key,
        application_name,
        created_by,
        interface_id,
        created_time
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.appKey},
        #{item.applicationName},
        #{item.createdBy},
        #{item.interfaceId},
        #{item.createdTime}
        )
        </foreach>
    </insert>
</mapper>
