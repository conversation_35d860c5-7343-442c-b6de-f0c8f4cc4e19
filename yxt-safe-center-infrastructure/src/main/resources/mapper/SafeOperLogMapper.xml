<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.safecenter.infrastructure.dao.mapper.SafeOperLogMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into safe_oper_log (
        id,
        title,
        business_type,
        method,
        request_method,
        operator_type,
        oper_name,
        oper_id,
        oper_url,
        oper_ip,
        oper_location,
        oper_param,
        json_result,
        status,
        error_msg,
        oper_time,
        cost_time
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.title},
        #{item.businessType},
        #{item.method},
        #{item.requestMethod},
        #{item.operatorType},
        #{item.operName},
        #{item.operId},
        #{item.operUrl},
        #{item.operIp},
        #{item.operLocation},
        #{item.operParam},
        #{item.jsonResult},
        #{item.status},
        #{item.errorMsg},
        #{item.operTime},
        #{item.costTime}
        )
        </foreach>
    </insert>
</mapper>
