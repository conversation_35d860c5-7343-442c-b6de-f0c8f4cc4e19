<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.safecenter.infrastructure.dao.mapper.SafeApiCallStatisticsMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into safe_api_call_statistics (
        id,
        application_name,
        api_path,
        api_method,
        gateway,
        app_key,
        auth_mode,
        app_auth_method,
        uni_key,
        total_calls,
        channel_interceptions,
        rbac_interceptions,
        auth_interceptions,
        period,
        start_time,
        end_time,
        created_time
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.applicationName},
        #{item.apiPath},
        #{item.apiMethod},
        #{item.gateway},
        #{item.appKey},
        #{item.authMode},
        #{item.appAuthMethod},
        #{item.uniKey},
        #{item.totalCalls},
        #{item.channelInterceptions},
        #{item.rbacInterceptions},
        #{item.authInterceptions},
        #{item.period},
        #{item.startTime},
        #{item.endTime},
        #{item.createdTime}
        )
        </foreach>
    </insert>

    <insert id="upsertBatch">
        INSERT INTO safe_api_call_statistics (
        application_name, api_path, api_method, gateway,
        app_key, auth_mode, app_auth_method, uni_key, total_calls,
        channel_interceptions, rbac_interceptions, auth_interceptions,
        period, start_time, end_time
        )
        VALUES
        <foreach collection="records" item="item" separator=",">
            (
            #{item.applicationName}, #{item.apiPath}, #{item.apiMethod},
            #{item.gateway}, #{item.appKey}, #{item.authMode}, #{item.appAuthMethod},
            #{item.uniKey}, #{item.totalCalls}, #{item.channelInterceptions},
            #{item.rbacInterceptions}, #{item.authInterceptions},
            #{item.period}, #{item.startTime}, #{item.endTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        total_calls = total_calls + VALUES(total_calls),
        channel_interceptions = channel_interceptions + VALUES(channel_interceptions),
        rbac_interceptions = rbac_interceptions + VALUES(rbac_interceptions),
        auth_interceptions = auth_interceptions + VALUES(auth_interceptions)
    </insert>

</mapper>
