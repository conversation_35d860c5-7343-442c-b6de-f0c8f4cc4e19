package com.yxt.safecenter.infrastructure.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.yxt.safecenter.common.model.UserContext;
import com.yxt.safecenter.common.model.bo.LoginInfoBO;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        LoginInfoBO context = UserContext.getContext();
        if (context != null) {
            Object createdByValue = getFieldValByName("createdBy", metaObject);
            if (createdByValue == null) {
                this.setFieldValByName("createdBy", context.getUserName(), metaObject);
            }
            Object updatedBy = getFieldValByName("updatedBy", metaObject);
            if (updatedBy == null) {
                this.setFieldValByName("updatedBy", context.getUserName(), metaObject);
            }
        }

    }

    @Override
    public void updateFill(MetaObject metaObject) {
        LoginInfoBO context = UserContext.getContext();
        if (context != null) {
            Object updatedBy = getFieldValByName("updatedBy", metaObject);
            if (updatedBy == null) {
                this.setFieldValByName("updatedBy", context.getUserName(), metaObject);
            }
        }
    }
}