package com.yxt.safecenter.infrastructure.dao.mapper;

import com.yxt.safecenter.infrastructure.dao.po.SafeOperLogPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
public interface SafeOperLogMapper extends BaseMapper<SafeOperLogPO> {
    /**
     * 批量插入
     */
    boolean insertBatch(@Param("records")List<SafeOperLogPO> SafeOperLogPOs);
}