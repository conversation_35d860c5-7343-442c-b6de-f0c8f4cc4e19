package com.yxt.safecenter.infrastructure.config.sharding;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.hash.HashCode;
import com.google.common.hash.Hashing;
import com.yxt.safecenter.common.lib.util.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import java.util.Collection;
import java.util.Optional;

@Slf4j
public class DataTablePreciseRule implements PreciseShardingAlgorithm<Long> {

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<Long> shardingValue) {
        //分表算法见于：https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=24623647
        //murmurhash算法打散
        HashCode hashCode = Hashing.murmur3_128().hashLong(shardingValue.getValue());

        //虚拟表，再映射到物理表，无特殊映射诉求，先采用均分区间映射
        ShardingTableRuleConfig shardingTableRuleConfig = SpringUtil.getBean(ShardingTableRuleConfig.class);
        long index = (Math.abs(hashCode.asLong()) % shardingTableRuleConfig.getVirtualTableNum()) /
                (shardingTableRuleConfig.getVirtualTableNum() / shardingTableRuleConfig.getPhysicalTableNum());

        Optional<String> hitTableName = availableTargetNames.stream().filter(name -> name.lastIndexOf(index + "") > -1).findFirst();
        if (hitTableName.isPresent()) {
            return hitTableName.get();
        }
        throw new RuntimeException("not found table");
    }

    ShardingTableRuleConfig getTableRuleConfig() {
        return SpringUtils.getBean(ShardingTableRuleConfig.class);
    }


}
