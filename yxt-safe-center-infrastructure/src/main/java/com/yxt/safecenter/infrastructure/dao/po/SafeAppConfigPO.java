package com.yxt.safecenter.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 应用配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@TableName("safe_app_config")
public class SafeAppConfigPO extends BasePO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用id
     */
    private String appKey;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 使用非对称方式鉴权密钥、TOKEN等
     */
    private String cerKey;

    /**
     * 鉴权方式 RSA-rsa/rsa2验签 MD5-md5验签 TOKENBS-验证token是否相同的特殊方式
     */
    private String authMethod;

    /**
     * 生效时间
     */
    private LocalDateTime startTime;

    /**
     * 失效时间
     */
    private LocalDateTime endTime;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     *  数据版本，每次update+1
     */
    private Long version;

}
