package com.yxt.safecenter.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 * 服务接口表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@TableName("safe_interface")
public class SafeInterfacePO extends BasePO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 服务名
     */
    private String applicationName;

    /**
     * api类
     */
    private String apiClass;

    /**
     * api方法
     */
    private String apiMethod;

    /**
     * 请求方式
     */
    private String apiWay;

    /**
     * path路径
     */
    private String apiPath;

    /**
     * API名称
     */
    private String apiName;

    /**
     * 请求参数信息
     */
    private String reqParamsInfo;

    /**
     * 响应参数信息
     */
    private String respParamsInfo;

    /**
     * 接口描述
     */
    private String apiDesc;

    /**
     * 权限相关 json
     */
    private String authInfo;

    /**
     * 是否统一加解密 ENC-加密 DENC-解密 ENC_DENC-加密解密
     */
    private String encryptionRequired;

    /**
     * 状态 上线中-ONLINE_RUNNING 已上线-ONLINE 下线中-OFFLINE_RUNNING 已下线-OFFLINE
     */
    private String status;

    /**
     * 数据版本，每次update+1
     */
    private Long version;

}
