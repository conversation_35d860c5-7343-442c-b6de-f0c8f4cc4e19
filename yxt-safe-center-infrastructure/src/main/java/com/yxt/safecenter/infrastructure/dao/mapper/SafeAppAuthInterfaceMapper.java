package com.yxt.safecenter.infrastructure.dao.mapper;

import com.yxt.safecenter.infrastructure.dao.po.SafeAppAuthInterfacePO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface SafeAppAuthInterfaceMapper extends BaseMapper<SafeAppAuthInterfacePO> {
    /**
     * 批量插入
     */
    boolean insertBatch(@Param("records")List<SafeAppAuthInterfacePO> SafeAppAuthInterfacePOs);
}