package com.yxt.safecenter.infrastructure.dao.mapper;

import com.yxt.safecenter.infrastructure.dao.po.SafeInterfacePO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface SafeInterfaceMapper extends BaseMapper<SafeInterfacePO> {
    /**
     * 批量插入
     */
    boolean insertBatch(@Param("records")List<SafeInterfacePO> SafeInterfacePOs);

    /**
     * 批量更新基本信息
     */
    int updateBaseBatch(@Param("records")List<SafeInterfacePO> SafeInterfacePOs);
}