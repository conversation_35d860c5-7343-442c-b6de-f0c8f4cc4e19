package com.yxt.safecenter.infrastructure.dao.mapper;

import com.yxt.safecenter.infrastructure.dao.po.SafeApiCallStatisticsPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface SafeApiCallStatisticsMapper extends BaseMapper<SafeApiCallStatisticsPO> {
    /**
     * 批量插入
     */
    boolean insertBatch(@Param("records") List<SafeApiCallStatisticsPO> SafeApiCallStatisticsPOs);

    // 新增批量upsert方法
    int upsertBatch(@Param("records") List<SafeApiCallStatisticsPO> records);
}