package com.yxt.safecenter.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 应用授权接口表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@TableName("safe_app_auth_interface")
public class SafeAppAuthInterfacePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用id
     */
    private String appKey;

    /**
     * 服务名
     */
    private String applicationName;

    /**
     * 接口id
     */
    private Long interfaceId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

}
