<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-safe-center-manager</artifactId>
    <name>yxt-safe-center-manager</name>
    <description>yxt-safe-center-manager</description>
    <parent>
        <groupId>com.yxt.safecenter</groupId>
        <artifactId>yxt-safe-center</artifactId>
        <version>${reversion}</version>
    </parent>

    <properties>
        <module.deploy.skip>true</module.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yxt.safecenter</groupId>
            <artifactId>yxt-safe-center-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.safecenter</groupId>
            <artifactId>yxt-safe-center-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.safecenter</groupId>
            <artifactId>yxt-safe-center-message-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

</project>
