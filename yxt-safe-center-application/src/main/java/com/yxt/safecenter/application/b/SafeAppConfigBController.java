package com.yxt.safecenter.application.b;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.redis.cache.RedisClient;
import com.yxt.safecenter.common.framework.log.aspectj.Log;
import com.yxt.safecenter.common.framework.log.model.enums.BusinessType;
import com.yxt.safecenter.common.model.bo.SafeAppConfigUpOrAddBO;
import com.yxt.safecenter.common.model.dto.req.SafeAppAuthInterfaceQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeAppAuthInterfaceUpReq;
import com.yxt.safecenter.common.model.dto.req.SafeAppConfigQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeAppConfigUpOrAddReq;
import com.yxt.safecenter.service.model.bo.SafeAppConfigEBO;
import com.yxt.safecenter.common.model.dto.resp.SafeAppConfigResp;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import com.yxt.safecenter.service.utils.excel.ExcelUtil;
import com.yxt.safecenter.service.model.FileEnum;
import com.yxt.lang.dto.PageBase;
import com.yxt.safecenter.service.SafeAppConfigService;
import com.yxt.safecenter.service.model.redis.BizRedisKeyGroup;
import com.yxt.safecenter.service.utils.BeanUtil;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 应用管理
 *
 * <AUTHOR>
 * @date 2025年02月08日 17:08
 */
@RestController
@RequestMapping(value = "/b/safeConfig")
@Api(tags = "B端应用管理")
@Slf4j
@RequiredArgsConstructor
public class SafeAppConfigBController extends AbstractController {

    private final SafeAppConfigService safeAppConfigService;
    private final RedisClient redisClient;

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping(value = "/page")
    public ResponseBase<PageDTO<SafeAppConfigResp>> page(@Validated SafeAppConfigQueryReq queryReq) {
        return generateSuccess(safeAppConfigService.page(queryReq));
    }

    @ApiOperation(value = "新增/更新", notes = "新增/更新")
    @PostMapping(value = "/saveOrUp")
    @Log(title = "应用管理", businessType = BusinessType.UPDATE)
    public ResponseBase<Void> saveOrUp(@RequestBody @Validated SafeAppConfigUpOrAddReq upOrAddReq) {
        safeAppConfigService.saveOrUp(BeanUtil.copyProperties(upOrAddReq, SafeAppConfigUpOrAddBO.class));
        return generateSuccess(null);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @GetMapping(value = "/del")
    public ResponseBase<Void> del(Long id) {
//        redisClient.set(BizRedisKeyGroup.CHANNEL_RELATION_LIST, "100", "100");
        Object o = redisClient.get(BizRedisKeyGroup.CHANNEL_RELATION_LIST, "100");
        redisClient.delete(BizRedisKeyGroup.CHANNEL_RELATION_LIST, "100");
        Object p = redisClient.get(BizRedisKeyGroup.CHANNEL_RELATION_LIST, "100");
        System.out.println(p);
        safeAppConfigService.del(id);
        return generateSuccess(null);
    }

    @ApiOperation(value = "授权接口_分页", notes = "授权接口_分页")
    @GetMapping(value = "/authInterfacePage")
    public ResponseBase<PageDTO<SafeInterfaceResp>> authInterfacePage(@Validated SafeAppAuthInterfaceQueryReq authInterfaceQueryReq) {
        return generateSuccess(safeAppConfigService.authInterfacePage(authInterfaceQueryReq));
    }

    @ApiOperation(value = "授权接口_添加/删除", notes = "授权接口_添加/删除")
    @PostMapping(value = "/authInterfaceUp")
    public ResponseBase<Void> authInterfaceUp(@RequestBody @Validated SafeAppAuthInterfaceUpReq interfaceAddReq) {
        safeAppConfigService.authInterfaceUp(interfaceAddReq);
        return generateSuccess(null);
    }

    @ApiOperation(value = "导入", notes = "导入应用配置，按照app_key进行新增/更新")
    @PostMapping(value = "/import")
    public ResponseBase<Void> imports(@RequestParam("file") MultipartFile multipartFile) {
        List<SafeAppConfigEBO> imports = ExcelUtil.read(multipartFile, SafeAppConfigEBO.class);
        safeAppConfigService.imports(imports);
        return generateSuccess(null);
    }
}
