package com.yxt.safecenter.application.b;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.model.dto.req.SetAuthInfoReq;
import com.yxt.safecenter.service.HandToolsService;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Since: 2025/05/07 15:10
 * Author: qs
 */

@RestController
@RequestMapping(value = "/b/handTools")
@Api(tags = "梳理数据工具")
@Slf4j
public class HandToolsController extends AbstractController {

    @Resource
    private HandToolsService handToolsService;

    @ApiOperation(value = "设置鉴权模式", notes = "设置鉴权模式")
    @PostMapping(value = "/setAuthInfo")
    public ResponseBase<Void> setAuthInfo(@RequestBody @Validated SetAuthInfoReq req) {
        handToolsService.setAuthInfo(req);
        return generateSuccess(null);
    }

    @ApiOperation(value = "设置鉴权模式，不覆盖原数据", notes = "设置鉴权模式")
    @PostMapping(value = "/setAuthInfoNoCover")
    public ResponseBase<Void> setAuthInfoNoCover(@RequestBody @Validated SetAuthInfoReq req) {
        handToolsService.setAuthInfoNoCover(req);
        return generateSuccess(null);
    }
}
