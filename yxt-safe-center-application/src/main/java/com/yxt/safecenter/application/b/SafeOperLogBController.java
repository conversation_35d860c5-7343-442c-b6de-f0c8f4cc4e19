package com.yxt.safecenter.application.b;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.framework.log.aspectj.Log;
import com.yxt.safecenter.common.framework.log.model.enums.BusinessType;
import com.yxt.safecenter.common.model.dto.req.SafeOperLogQueryReq;
import com.yxt.safecenter.common.model.dto.resp.SafeOperLogResp;
import com.yxt.safecenter.service.SafeOperLogService;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 应用管理
 *
 * <AUTHOR>
 * @date 2025年02月08日 17:08
 */
@RestController
@RequestMapping(value = "/b/operLog")
@Api(tags = "B端操作日志管理")
@Slf4j
@RequiredArgsConstructor
public class SafeOperLogBController extends AbstractController {

    private final SafeOperLogService safeOperLogService;

    @GetMapping("/page")
    @ApiOperation(value = "操作日志分页查询", notes = "操作日志分页查询")
    public ResponseBase<PageDTO<SafeOperLogResp>> page(SafeOperLogQueryReq queryReq) {
        return generateSuccess(safeOperLogService.page(queryReq));
    }

    @Log(title = "操作日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    @ApiOperation(value = "清楚操作日志", notes = "清楚操作日志")
    public ResponseBase<Void> clean() {
        safeOperLogService.cleanOperLog();
        return generateSuccess(null);
    }
}
