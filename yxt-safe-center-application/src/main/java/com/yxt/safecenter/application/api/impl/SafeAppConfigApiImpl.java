package com.yxt.safecenter.application.api.impl;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.model.dto.req.SafeEnableAppConfigInterfacePageReq;
import com.yxt.safecenter.common.model.dto.resp.SafeAppConfigInterfaceApiResp;
import com.yxt.safecenter.feign.sdk.api.SafeAppConfigApi;
import com.yxt.safecenter.service.SafeAppConfigService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

/**
 * Since: 2025/2/25 14:33
 * Author: qs
 */

@RestController
@Api(tags = "应用配置")
public class SafeAppConfigApiImpl implements SafeAppConfigApi {

    @Resource
    private SafeAppConfigService safeAppConfigService;

    @Override
    public ResponseBase<PageDTO<SafeAppConfigInterfaceApiResp>> enableAppConfigPage(SafeEnableAppConfigInterfacePageReq pageReq) {
        return ResponseBase.success(safeAppConfigService.enableAppConfigInterfacePage(pageReq));
    }
}
