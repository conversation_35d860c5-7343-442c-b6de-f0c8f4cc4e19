package com.yxt.safecenter.application.api.impl;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.model.dto.req.ApiCallStatisticsReq;
import com.yxt.safecenter.feign.sdk.api.ApiStatisticsApi;
import com.yxt.safecenter.service.ApiCallStatisticsService;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Since: 2025/2/14 15:10
 * Author: qs
 */

@RestController
@Api(tags = "数据统计")
public class ApiStatisticsApiImpl implements ApiStatisticsApi {

    @Resource
    private ApiCallStatisticsService apiCallStatisticsService;

    @Override
    public ResponseBase<Void> apiStatisticsSaveBatch(@Validated List<ApiCallStatisticsReq> reqList) {
        apiCallStatisticsService.apiStatisticsSaveBatch(reqList);
        return new ResponseBase<>();
    }
}
