package com.yxt.safecenter.application;

import cn.hutool.core.util.URLUtil;
import com.yxt.lang.exception.YxtNotLoginException;
import com.yxt.starter.controller.AbstractController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * Since: 2025/07/29 10:18
 * Author: qs
 */
public abstract class UserAbstractController extends AbstractController {

    protected final static String HEAD_USER_ID_KEY = "userId";
    protected final static String HEAD_EMP_CODE_KEY = "empcode";
    protected final static String HEAD_USER_ZH_NAME_KEY = "userzhname";
    protected final static String HEAD_USER_NAME_KEY = "username";

    /**
     * 获取userId（请求头）
     */
    public static String getUserId() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            return request.getHeader(HEAD_USER_ID_KEY);
        }
        return null;
    }

    /**
     * 获取userName（请求头）
     */
    public static String getUserName() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            return request.getHeader(HEAD_USER_NAME_KEY);
        }
        return null;
    }

    /**
     * 获取userZHName（请求头）
     */
    public static String getUserZHName() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            String userZHName = request.getHeader(HEAD_USER_ZH_NAME_KEY);
            if (StringUtils.isBlank(userZHName)) {
                return getUserName();
            } else {
                return URLUtil.decode(userZHName);
            }
        }
        return null;
    }

    /**
     * 获取erpCode（请求头），为空报异常
     */
    public static String getEmpCodeWithErr() {
        String empCode = getEmpCode();
        if (StringUtils.isEmpty(empCode)) {
            throw new YxtNotLoginException("请传入工号");
        }
        return empCode;
    }

    /**
     * 获取erpCode（请求头）
     */
    public static String getEmpCode() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            return request.getHeader(HEAD_EMP_CODE_KEY);
        }
        return null;
    }

    /**
     * 获取userId（请求头），为空报异常
     */
    public static String getUserIdWithError() {
        String userId = getUserId();
        if (userId == null) {
            throw new YxtNotLoginException();
        }
        return userId;
    }

}
