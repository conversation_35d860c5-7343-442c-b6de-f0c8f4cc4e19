package com.yxt.safecenter.application.b;

import com.google.common.collect.Lists;
import com.yxt.lang.dto.PageBase;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.application.UserAbstractController;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceUpReq;
import com.yxt.safecenter.common.model.dto.resp.AuthSelectTreeResp;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import com.yxt.safecenter.service.SafeInterfaceService;
import com.yxt.safecenter.service.model.FileEnum;
import com.yxt.safecenter.service.model.bo.SafeInterfaceEBO;
import com.yxt.safecenter.service.utils.excel.ExcelUtil;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/b/safeInterface")
@Api(tags = "B端接口管理")
@Slf4j
@RequiredArgsConstructor
public class SafeInterfaceBController extends UserAbstractController {

    private final SafeInterfaceService safeInterfaceService;

    @ApiOperation(value = "应用名称集合", notes = "应用名称集合")
    @GetMapping(value = "/listApplicationName")
    public ResponseBase<List<String>> listApplicationName(String applicationName) {
        return generateSuccess(safeInterfaceService.listApplicationName(applicationName));
    }

    @ApiOperation(value = "分页查询接口信息", notes = "分页查询接口信息")
    @GetMapping(value = "/page")
    public ResponseBase<PageDTO<SafeInterfaceResp>> page(SafeInterfaceQueryReq queryReq) {
        return generateSuccess(safeInterfaceService.page(getEmpCodeWithErr(), queryReq));
    }

    @ApiOperation(value = "更新接口信息", notes = "更新接口信息")
    @PostMapping(value = "/upBase")
    public ResponseBase<Void> upBase(@RequestBody @Validated SafeInterfaceUpReq updateReq) {
        safeInterfaceService.upBase(updateReq);
        return generateSuccess(null);
    }

    @ApiOperation(value = "审核", notes = "审核 up:true[上线],up:false[下线]")
    @GetMapping(value = "/upStatus")
    public ResponseBase<Void> upStatus(Long[] ids, Boolean up) {
        safeInterfaceService.upStatus(Lists.newArrayList(ids), up);
        return generateSuccess(null);
    }

    @GetMapping("/export")
    @ApiOperation(value = "导出", notes = "导出")
    public void export(SafeInterfaceQueryReq queryReq, HttpServletResponse httpServletResponse) {
        ExcelUtil.exportByFileStream(FileEnum.EXCEL_XLS.getSuffix(), SafeInterfaceEBO.class, queryReq,
                () -> safeInterfaceService.pageExport(getEmpCodeWithErr(), queryReq), httpServletResponse, 100000);
    }

    @GetMapping("/exportTemp")
    @ApiOperation(value = "导出模版", notes = "导出模版")
    public void exportTemp(HttpServletResponse httpServletResponse) {
        ExcelUtil.exportByFileStream(FileEnum.EXCEL_XLS.getSuffix(), SafeInterfaceEBO.class, new PageBase(),
                () -> new PageDTO<>(), httpServletResponse, 0);
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入", notes = "导入")
    public ResponseBase<Void> imports(@RequestParam("file") MultipartFile multipartFile) {
        List<SafeInterfaceEBO> imports = ExcelUtil.read(multipartFile, SafeInterfaceEBO.class);
        safeInterfaceService.imports(imports);
        return ResponseBase.success();
    }

    @GetMapping("/getAuthSelectInfo")
    @ApiOperation(value = "查询网关鉴权选择信息", notes = "查询网关鉴权选择信息")
    public ResponseBase<List<AuthSelectTreeResp>> getAuthSelectInfo() {
        return generateSuccess(safeInterfaceService.getAuthSelectInfo());
    }
}
