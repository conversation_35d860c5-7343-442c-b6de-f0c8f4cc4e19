package com.yxt.safecenter.application.convert;

import cn.hutool.json.JSONUtil;
import com.yxt.safecenter.feign.sdk.dto.response.DemoResp;
import com.yxt.safecenter.service.model.bo.DemoBO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

@Component
@Mapper(componentModel = "spring",
        unmappedSourcePolicy = ReportingPolicy.IGNORE, unmappedTargetPolicy = ReportingPolicy.IGNORE,
        imports = {LocalDateTime.class, JSONUtil.class, Date.class})
public interface DemoAppEntityConvert {

    DemoAppEntityConvert INSTANCE = Mappers.getMapper(DemoAppEntityConvert.class);


    //    @Mapping(source = "name", target = "name")
    DemoResp toDictResp(DemoBO demoBO);

    DemoBO toDictBO(DemoResp resp);
}
