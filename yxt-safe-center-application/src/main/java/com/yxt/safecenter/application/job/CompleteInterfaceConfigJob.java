package com.yxt.safecenter.application.job;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.lang.util.DateHelper;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.common.lib.util.LocalDateUtils;
import com.yxt.safecenter.service.CompleteInterfaceConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 历史接口配置数据补全
 * Since: 2025/2/19 9:52
 * Author: qs
 */

@Component
public class CompleteInterfaceConfigJob {

    @Resource
    private CompleteInterfaceConfigService completeInterfaceConfigService;

    /**
     *  一天一次，不传参使用前一天调用记录数据
     *  传入参数可指定日期 格式 beginTime,endTime：yyyyMMddHHmmss,yyyyMMddHHmmss
     */
    @XxlJob("completeInterfaceConfigJob")
    public void execute() {
        try {
            //获取执行参数
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("收到 completeInterfaceConfigJob 历史接口配置补全调度，param:{}, 当前时间：{}",
                    param, LocalDateTimeUtil.format(LocalDateTime.now(), DateHelper.DATETIME_FORMAT));
            String beginTime = null;
            String endTime = null;
            if (StringUtils.isNotEmpty(param)) {
                String[] split = param.split(",");
                beginTime = split[0];
                if (split.length == 2) {
                    endTime = split[1];
                }
            } else {
                LocalDate beginLocalDate = LocalDate.now().minusDays(1);
                beginTime = LocalDateTimeUtil.format(LocalDateTime.of(beginLocalDate, LocalTime.MIN), LocalDateUtils.Pattern.YYYYMMDDHHMMSS);
                endTime = LocalDateTimeUtil.format(LocalDateTime.of(beginLocalDate, LocalTime.MAX), LocalDateUtils.Pattern.YYYYMMDDHHMMSS);
            }
            completeInterfaceConfigService.completeInterfaceConfig(beginTime, endTime);
            XxlJobHelper.log("完成 completeInterfaceConfigJob 历史接口配置补全调度，param:{}, 结束时间：{}",
                    param, LocalDateTimeUtil.format(LocalDateTime.now(), DateHelper.DATETIME_FORMAT));
        } catch (Exception e) {
            ExLogger.logger("completeInterfaceConfigJob").error("历史接口配置补全任务异常", e);
            XxlJobHelper.handleFail("历史接口配置补全任务异常");
        }
    }
}
