package com.yxt.safecenter.application.c;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.application.convert.DemoAppEntityConvert;
import com.yxt.safecenter.feign.sdk.dto.request.DemoReq;
import com.yxt.safecenter.feign.sdk.dto.response.DemoResp;
import com.yxt.safecenter.service.DemoService;
import com.yxt.safecenter.service.model.bo.DemoBO;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@RestController
@RequestMapping(value = "/c/demo")
@Api(tags = "C端接口Demo")
@Slf4j
public class DemoCController extends AbstractController {

    @Resource
    private DemoService service;
    @Autowired
    private DemoAppEntityConvert demoAppEntitytConvert;


    @ApiOperation(value = "c端demo-通过词典ID查询慢病词典信息", notes = "c端demo-通过词典ID查询慢病词典信息")
    @PostMapping(value = "/getById/{id3}/{id2}")
    ResponseBase<DemoResp> getByIdFromMysql(
            @PathVariable DemoReq id3,
            @PathVariable(name = "id2") String id1,
            @RequestParam(name = "pa", required = false) String paramTest,
            String paramTest1,
            DemoReq demoR,
            @RequestHeader(required = false) String headerTest,
            @RequestHeader(required = false, name = "he") String headerTest1,
            @RequestBody DemoResp desp, @RequestBody DemoReq demo, @RequestBody List<String> demoList) {
//        DemoBO demoBO = service.selectDictById(demoReq.getDiseaseDictId());
        DemoResp resp = new DemoResp();
        resp.setId(23123123);
        resp.setName("测试的");
        return generateSuccess(resp);
    }
}
