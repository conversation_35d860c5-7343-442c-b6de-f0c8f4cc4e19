package com.yxt.safecenter.application.api.impl;

import cn.hutool.core.bean.BeanUtil;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lang.util.ExLogger;
import com.yxt.redis.lock.client.LockTemplate;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceQueryReq;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceApiResp;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import com.yxt.safecenter.feign.sdk.dto.request.ApiInterfaceQueryReq;
import com.yxt.safecenter.sdk.online.api.SafeInterfaceApi;
import com.yxt.safecenter.sdk.online.dto.request.SafeInterfaceOnlineReq;
import com.yxt.safecenter.service.SafeInterfaceService;
import com.yxt.safecenter.service.model.bo.SafeInterfaceOnlineBO;
import com.yxt.safecenter.service.model.redis.BizRedisKeyGroup;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

/**
 * .
 *
 * <AUTHOR>
 * @date 2025年01月07日 16:13
 */
@RestController
@Api(tags = {"接口上报", "接口查询"})
@Slf4j
@RequiredArgsConstructor
public class SafeInterfaceApiImpl implements SafeInterfaceApi, com.yxt.safecenter.feign.sdk.api.SafeInterfaceApi {

    private final SafeInterfaceService safeInterfaceService;
    private final LockTemplate lockTemplate;

    @Override
    public ResponseBase<Void> online(List<SafeInterfaceOnlineReq> onlineReqList) {
        if (CollectionUtils.isEmpty(onlineReqList)) {
            return new ResponseBase<>();
        }
        try {
            lockTemplate.executeLock(BizRedisKeyGroup.CHANNEL_RELATION_TEST, onlineReqList.get(0).getApplicationName(), 10000, 0,
                    () -> {
                        ExLogger.logger().info("服务{}，接口数：{}", onlineReqList.get(0).getApplicationName(), onlineReqList.size());
                        safeInterfaceService.online(BeanUtil.copyToList(onlineReqList, SafeInterfaceOnlineBO.class));
                    });
        } catch (Exception e) {
            log.warn("接口上报 锁异常", e);
        }
        return new ResponseBase<>();
    }

    @Override
    public ResponseBase<List<SafeInterfaceApiResp>> listByApplicationName(String applicationName) {
        return ResponseBase.success(BeanUtil.copyToList(safeInterfaceService.listByApplicationName(applicationName), SafeInterfaceApiResp.class));
    }

    @Override
    public ResponseBase<PageDTO<SafeInterfaceResp>> page(String empCode, ApiInterfaceQueryReq req) {
        return ResponseBase.success(safeInterfaceService.page(empCode, BeanUtil.copyProperties(req, SafeInterfaceQueryReq.class)));
    }

    @Override
    public ResponseBase<List<SafeInterfaceResp>> listByIds(String empCode, Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return ResponseBase.success(Collections.emptyList());
        }
        if (ids.size() > 200) {
            throw new YxtBizException("id数量不能大于200");
        }
        return ResponseBase.success(safeInterfaceService.listByIds(empCode, new HashSet<>(ids)));
    }
}
