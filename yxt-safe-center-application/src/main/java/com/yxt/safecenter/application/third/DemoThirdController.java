package com.yxt.safecenter.application.third;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.application.convert.DemoAppEntityConvert;
import com.yxt.safecenter.feign.sdk.dto.request.DemoReq;
import com.yxt.safecenter.feign.sdk.dto.response.DemoResp;
import com.yxt.safecenter.service.DemoService;
import com.yxt.safecenter.service.model.bo.DemoBO;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping(value = "/third/demo")
@Api(tags = "三方接口Demo")
@Slf4j
public class DemoThirdController extends AbstractController {

    @Resource
    private DemoService service;
    @Autowired
    private DemoAppEntityConvert demoAppEntitytConvert;


    @ApiOperation(value = "三方demo-通过词典ID查询慢病词典信息", notes = "三方demo-通过词典ID查询慢病词典信息")
    @PostMapping(value = "/getById")
    ResponseBase<DemoResp> getByIdFromMysql(@RequestBody DemoReq demoReq){
        DemoBO demoBO = service.selectDictById(demoReq.getDiseaseDictId());
        return generateSuccess(demoAppEntitytConvert.toDictResp(demoBO));
    }
}
