package com.yxt.safecenter.application.b;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.model.dto.resp.MetaKeyValueResp;
import com.yxt.safecenter.service.utils.EnumUtils;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 应用管理
 *
 * <AUTHOR>
 * @date 2025年02月08日 17:08
 */
@RestController
@RequestMapping(value = "/b/enums")
@Api(tags = "B端枚举统一获取管理")
@Slf4j
@RequiredArgsConstructor
public class SafeEnumsBController extends AbstractController {


    @ApiOperation(value = "根据枚举名称查询枚举集合信息", notes = "根据枚举名称查询枚举集合信息")
    @GetMapping(value = "/getByEnumName")
    public ResponseBase<List<MetaKeyValueResp<String, Object, String>>> getByEnumName(String enumName) {
        return generateSuccess(EnumUtils.getByEnumName(enumName));
    }

    @ApiOperation(value = "查询所有枚举集合信息", notes = "查询所有枚举集合信息")
    @GetMapping(value = "/getAllEnums")
    public ResponseBase<Map<String, List<MetaKeyValueResp<String, Object, String>>>> getAllEnums() {
        return generateSuccess(EnumUtils.getAllEnums());
    }
}
