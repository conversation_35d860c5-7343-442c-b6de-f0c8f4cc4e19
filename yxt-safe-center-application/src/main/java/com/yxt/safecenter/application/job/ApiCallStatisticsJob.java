package com.yxt.safecenter.application.job;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.lang.util.DateHelper;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.service.ApiCallStatisticsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * API统计数据job
 * Since: 2025/2/19 9:52
 * Author: qs
 */

@Component
public class ApiCallStatisticsJob {


    @Resource
    private ApiCallStatisticsService apiCallStatisticsService;

    /**
     * 清理api统计历史数据
     * 每天执行一次
     * 不传参数默认保留30天，传入参数指定保留天数
     */
    @XxlJob("clearApiCallStatisticsData")
    public void clearApiCallStatistics() {
        try {
            //获取执行参数
            // 保留时间
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("收到 clearApiCallStatisticsData 清理api统计历史数据，param:{}, 开始时间：{}",
                    param, LocalDateTimeUtil.format(LocalDateTime.now(), DateHelper.DATETIME_FORMAT));
            if (StringUtils.isEmpty(param)) {
                // 默认30天
                param = "30";
            }
            LocalDateTime threshold  = LocalDateTime.now().minusDays(30);
            apiCallStatisticsService.clearOldData(threshold );
            XxlJobHelper.log("完成 clearApiCallStatisticsData 清理api统计历史数据，param:{}, 结束时间：{}",
                    param, LocalDateTimeUtil.format(LocalDateTime.now(), DateHelper.DATETIME_FORMAT));
        } catch (Exception e) {
            ExLogger.logger("completeInterfaceConfigJob").error("清理api统计历史数据任务异常", e);
            XxlJobHelper.handleFail("清理api统计历史数据异常");
        }
    }
}
