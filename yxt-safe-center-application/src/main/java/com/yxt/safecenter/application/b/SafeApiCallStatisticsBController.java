package com.yxt.safecenter.application.b;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.model.dto.req.SafeApiCallStatisticsPageReq;
import com.yxt.safecenter.common.model.dto.resp.SafeApiCallStatisticsResp;
import com.yxt.safecenter.service.ApiCallStatisticsService;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Since: 2025/2/21 16:56
 * Author: qs
 */
@RestController
@RequestMapping(value = "/b/safeApiCallStatistics")
@Api(tags = "B端数据统计")
@Slf4j
@RequiredArgsConstructor
public class SafeApiCallStatisticsBController extends AbstractController {

    private final ApiCallStatisticsService apiCallStatisticsService;

    @ApiOperation(value = "分页查询接口统计信息", notes = "分页查询接口统计信息")
    @GetMapping(value = "/page")
    public ResponseBase<PageDTO<SafeApiCallStatisticsResp>> page(@Validated SafeApiCallStatisticsPageReq pageReq) {
        return generateSuccess(apiCallStatisticsService.page(pageReq));
    }
}
