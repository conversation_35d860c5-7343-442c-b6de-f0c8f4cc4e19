<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-safe-center-common-model</artifactId>
    <!--    <version>${reversion}</version>-->
    <name>yxt-safe-center-common-model</name>
    <description>yxt-safe-center-common-model</description>

    <parent>
        <groupId>com.yxt.safecenter</groupId>
        <artifactId>yxt-safe-center-common</artifactId>
        <version>${reversion}</version>
    </parent>

    <properties>
        <module.deploy.skip>false</module.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- swagger -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
    </dependencies>

</project>
