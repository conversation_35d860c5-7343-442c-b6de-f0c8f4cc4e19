package com.yxt.safecenter.common.model.bo;


import com.yxt.safecenter.common.model.enums.AuthMethodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
public class SafeAppConfigBaseUpBO {

    @ApiModelProperty(value = "主键 更新时候传递")
    private Long id;

    /**
     * 鉴权方式 RSA-rsa/rsa2验签 MD5-md5验签 TOKENBS-验证token是否相同的特殊方式
     */
    @ApiModelProperty(value = "鉴权方式 RSA-rsa/rsa2验签 MD5-md5验签 TOKENBS-验证token是否相同的特殊方式")
    private AuthMethodEnum authMethod;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    private LocalDateTime startTime;

    /**
     * 失效时间
     */
    @ApiModelProperty(value = "失效时间")
    private LocalDateTime endTime;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Boolean enable;
}
