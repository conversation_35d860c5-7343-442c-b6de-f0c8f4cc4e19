package com.yxt.safecenter.common.model.dto.req;

import com.yxt.lang.dto.PageBase;
import com.yxt.safecenter.common.model.enums.AuthGatewayEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 *
 * Since: 2025/2/21 16:34
 * Author: qs
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("数据统计分页查询参数")
public class SafeApiCallStatisticsPageReq extends PageBase {

    @ApiModelProperty(value = "服务名")
    private String applicationName;

    @ApiModelProperty(value = "PATH路径")
    private String apiPath;

    @ApiModelProperty(value = "调用网关")
    private AuthGatewayEnum gateway;

    @NotNull(message = "统计开始时间不能为空")
    @ApiModelProperty(value = "统计开始时间")
    private LocalDateTime beginTime;

    @NotNull(message = "统计结束时间不能为空")
    @ApiModelProperty(value = "统计结束时间")
    private LocalDateTime endTime;
}
