package com.yxt.safecenter.common.model.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Since: 2025/2/14 14:47
 * Author: qs
 */

@ApiModel
@Data
@EqualsAndHashCode(callSuper = false)
public class ApiCallStatisticsReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务名
     */
    @ApiModelProperty(value = "服务名")
    @NotEmpty(message = "服务名不能为空")
    private String applicationName;

    /**
     * 实际请求path路径，只有一个值
     */
    @ApiModelProperty(value = "实际请求path路径")
    @NotEmpty(message = "实际请求path路径不能为空")
    private String apiPath;

    /**
     * 实际请求方式，只有一个值
     */
    @ApiModelProperty(value = "实际请求方式")
    @NotEmpty(message = "实际请求方式不能为空")
    private String apiMethod;

    /**
     * 实际调用网关 C-小程序网关 API-对外网关  BIGDATA-大数据网关 BUS-B端用户网关
     */
    @ApiModelProperty(value = "实际调用网关 C-小程序网关 API-对外网关  BIGDATA-大数据网关 BUS-B端用户网关")
    @NotEmpty(message = "实际调用网关不能为空")
    private String gateway;

    /**
     * 调用的appKey
     */
    @ApiModelProperty(value = "调用的appKey")
    private String appKey;

    /**
     * 调用的鉴权模式 SESSION-会话 SIGN-验签 为空不鉴权
     */
    @ApiModelProperty(value = "调用的鉴权模式 SESSION-会话 SIGN-验签 为空不鉴权")
    private String authMode;

    /**
     * 应用授权方式
     */
    @ApiModelProperty(value = "应用授权方式")
    private String appAuthMethod;

    /**
     * 总调用次数
     */
    @ApiModelProperty(value = "总调用次数")
    @NotNull(message = "总调用次数不能为空")
    private Integer totalCalls;

    /**
     * 调用渠道不匹配拦截次数
     */
    @ApiModelProperty(value = "调用渠道不匹配拦截次数")
    @NotNull(message = "调用渠道不匹配拦截次数不能为空")
    private Integer channelInterceptions;

    /**
     * rbac鉴权拦截次数
     */
    @ApiModelProperty(value = "rbac鉴权拦截次数")
    @NotNull(message = "rbac鉴权拦截次数不能为空")
    private Integer rbacInterceptions;

    /**
     * 认证鉴权拦截次数
     */
    @ApiModelProperty(value = "认证鉴权拦截次数")
    @NotNull(message = "认证鉴权拦截次数不能为空")
    private Integer authInterceptions;

    /**
     * 统计周期
     */
    @ApiModelProperty(value = "统计周期")
    @NotEmpty(message = "统计周期不能为空")
    private String period;

    /**
     * 统计开始时间
     */
    @ApiModelProperty(value = "统计开始时间")
    @NotNull(message = "统计开始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 统计结束时间
     */
    @ApiModelProperty(value = "统计结束时间")
    @NotNull(message = "统计结束时间不能为空")
    private LocalDateTime endTime;
}
