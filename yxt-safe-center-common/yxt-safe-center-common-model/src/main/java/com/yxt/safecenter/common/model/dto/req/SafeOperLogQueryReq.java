package com.yxt.safecenter.common.model.dto.req;

import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 操作日志记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Data
public class SafeOperLogQueryReq extends PageBase {

    @ApiModelProperty(value = "模块标题", example = "用户管理", required = true)
    private String title;

    @ApiModelProperty(value = "业务类型（0: 其它, 1: 新增, 2: 修改, 3: 删除...）", example = "1", required = true)
    private Integer businessType;

    @ApiModelProperty(value = "操作人员", example = "admin", required = true)
    private String operName;


    @ApiModelProperty(value = "操作地址", example = "***********", required = true)
    private String operIp;

    @ApiModelProperty(value = "操作状态（0: 正常, 1: 异常）", example = "0", required = true)
    private Integer status;

    @ApiModelProperty(value = "操作时间-开始")
    private LocalDateTime operTimeStart;
    @ApiModelProperty(value = "操作时间-结束")
    private LocalDateTime operTimeEnd;

}