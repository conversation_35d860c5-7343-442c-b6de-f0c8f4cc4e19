package com.yxt.safecenter.common.model.dto.req;


import com.yxt.safecenter.common.model.enums.AuthMethodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
public class SafeAppConfigUpOrAddReq {

    @ApiModelProperty(value = "主键 更新时候传递")
    private Long id;

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    @NotBlank(message = "应用id 不能为空")
    private String appKey;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    @NotBlank(message = "应用名称 不能为空")
    private String appName;

    /**
     * 使用非对称方式鉴权密钥、TOKEN等
     */
    @ApiModelProperty(value = "使用非对称方式鉴权密钥、TOKEN等")
    private String cerKey;

    /**
     * 鉴权方式 RSA-rsa/rsa2验签 MD5-md5验签 TOKENBS-验证token是否相同的特殊方式
     */
    @ApiModelProperty(value = "鉴权方式 RSA-rsa/rsa2验签 MD5-md5验签 TOKENBS-验证token是否相同的特殊方式")
    @NotNull(message = "鉴权方式 不能为空")
    private AuthMethodEnum authMethod;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    private LocalDateTime startTime;

    /**
     * 失效时间
     */
    @ApiModelProperty(value = "失效时间")
    private LocalDateTime endTime;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Boolean enable;
}
