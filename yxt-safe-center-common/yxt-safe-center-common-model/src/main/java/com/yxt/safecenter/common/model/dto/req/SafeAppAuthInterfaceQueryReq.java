package com.yxt.safecenter.common.model.dto.req;


import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
public class SafeAppAuthInterfaceQueryReq extends PageBase {

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    @NotEmpty(message = "应用id 不能为空")
    private String appKey;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "服务名")
    private String applicationName;
}
