package com.yxt.safecenter.common.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 是否统一加解密
 * Since: 2025/2/7 18:22
 * Author: qs
 */
public enum EncryptionRequiredEnum implements BaseEnum {
    /**
     *
     */
    ENC("加密"),
    DENC("解密"),
    ENC_DENC("加密解密"),
    ;
    private final String desc;

    EncryptionRequiredEnum(String desc) {
        this.desc = desc;
    }

    public static String getDescByName(String name) {
        if (name == null) {
            return null;
        }
        try {
            return valueOf(name).desc;
        } catch (Exception ignored) {

        }
        return null;
    }

    public static RbacValidationEnum getByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        return Arrays.stream(RbacValidationEnum.values())
                .filter(e -> e.name().equals(name))
                .findFirst()
                .orElse(null);
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getCode() {
        return name();
    }
}
