package com.yxt.safecenter.common.model.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * Since: 2025/2/25 16:59
 * Author: qs
 */

@Data
public class SafeInterfaceApiResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键", example = "1", required = true)
    private Long id;

    @ApiModelProperty(value = "服务名", example = "UserService", required = true)
    private String applicationName;

    @ApiModelProperty(value = "API方法", example = "getUser", required = true)
    private String apiMethod;

    @ApiModelProperty(value = "请求方式", example = "GET", required = true)
    private String apiWay;

    @ApiModelProperty(value = "path路径", example = "/users/{id}", required = true)
    private String apiPath;

    /**
     * 权限相关 json
     */
    private String authInfo;

    @ApiModelProperty(value = "是否统一加解密", example = "ENC", notes = "ENC-加密，DENC-解密，ENC_DENC-加密解密")
    private String encryptionRequired;

    @ApiModelProperty(value = "状态", example = "ONLINE", notes = "上线中-ONLINE_RUNNING，已上线-ONLINE，下线中-OFFLINE_RUNNING，已下线-OFFLINE_RUNNING")
    private String status;

}

