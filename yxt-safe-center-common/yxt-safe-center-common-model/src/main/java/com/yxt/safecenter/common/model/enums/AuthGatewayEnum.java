package com.yxt.safecenter.common.model.enums;

import com.yxt.safecenter.common.model.dto.resp.AuthSelectTreeResp;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public enum AuthGatewayEnum implements BaseEnum {

    /**
     *
     */
    B("心云网关/PC网关", Arrays.asList(AuthModeEnum.SESSION_B, AuthModeEnum.NO)),

    C("C端（小程序）网关",
            Arrays.asList(AuthModeEnum.SESSION_C, AuthModeEnum.SESSION_C_WEAK, AuthModeEnum.NO)),

    ASSIST("助手网关/移动端网关", Arrays.asList(AuthModeEnum.SESSION_B, AuthModeEnum.NO)),

    API("对外网关", Arrays.asList(AuthModeEnum.APP, AuthModeEnum.NO)),

    BIGDATA("大数据埋点网关", Arrays.asList(AuthModeEnum.SESSION_B, AuthModeEnum.NO))
    ;

    private final String desc;
    @Getter
    private final List<AuthModeEnum> authModes;

    AuthGatewayEnum(String desc, List<AuthModeEnum> authModes) {
        this.desc = desc;
        this.authModes = authModes;
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getCode() {
        return name();
    }

    public static AuthGatewayEnum getByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        return Arrays.stream(AuthGatewayEnum.values())
                .filter(e -> e.name().equals(name))
                .findFirst()
                .orElse(null);
    }

    public static String getDescByName(String name) {
        if (name == null) {
            return null;
        }
        try {
            return valueOf(name).desc;
        } catch (Exception ignored) {

        }
        return null;
    }

    /**
     * 枚举转换为 DTO
     */
    public AuthSelectTreeResp convertToDTO(AuthGatewayEnum authEnum) {
        List<AuthSelectTreeResp.AuthModeResp> authModes = authEnum.getAuthModes().stream()
                .map(e -> {
                    List<AuthSelectTreeResp.AuthModeResp.RbacValidationResp> rbacValidations = e.getRbacValidations().stream()
                            .map(rbac -> new AuthSelectTreeResp.AuthModeResp.RbacValidationResp(rbac.getCode(), rbac.getDesc()))
                            .collect(Collectors.toList());
                    return new AuthSelectTreeResp.AuthModeResp(e.getCode(), e.getDesc(), rbacValidations);
                })
                .collect(Collectors.toList());
        return new AuthSelectTreeResp(authEnum.name(), authEnum.getDesc(), authModes);
    }
}
