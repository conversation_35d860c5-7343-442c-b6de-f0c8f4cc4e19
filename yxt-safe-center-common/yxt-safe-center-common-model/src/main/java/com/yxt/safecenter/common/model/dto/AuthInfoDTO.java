package com.yxt.safecenter.common.model.dto;

import com.yxt.safecenter.common.model.enums.AuthGatewayEnum;
import com.yxt.safecenter.common.model.enums.AuthModeEnum;
import com.yxt.safecenter.common.model.enums.RbacValidationEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * .
 *
 * <AUTHOR>
 * @date 2025年03月21日 10:36
 */
@Data
public class AuthInfoDTO {

    @ApiModelProperty(value = "允许调用网关    C(\"小程序网关\"),\n" +
            "    API(\"对外网关\"),\n" +
            "    BIGDATA(\"大数据网关\"),\n" +
            "    BUS(\"B端用户网关\"),")
    private AuthGatewayEnum authGateway;

    @ApiModelProperty(value = " SESSION_B(\"B端用户Token鉴权\"),\n" +
            "    SESSION_C(\"C端用户Token鉴权\"),\n" +
            "    SESSION_C_WEAK(\"C端用户弱鉴权\"),\n" +
            "    APP(\"应用鉴权\"),\n" +
            "    NO(\"不鉴权\"),")
    private AuthModeEnum authMode;

    @ApiModelProperty(value = "RBAC权限 INTERFACE(\"接口校验\"),\n" +
            "    DATA(\"数据统一处理\"),\n" +
            "    ALL(\"全部\"),")
    private RbacValidationEnum rbacValidation;
}
