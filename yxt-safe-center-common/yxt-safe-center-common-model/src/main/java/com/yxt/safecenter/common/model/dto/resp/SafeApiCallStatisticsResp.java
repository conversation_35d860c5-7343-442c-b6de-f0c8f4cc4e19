package com.yxt.safecenter.common.model.dto.resp;

import com.yxt.safecenter.common.model.enums.AuthGatewayEnum;
import com.yxt.safecenter.common.model.enums.AuthMethodEnum;
import com.yxt.safecenter.common.model.enums.AuthModeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 接口数据统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@ApiModel
@Data
public class SafeApiCallStatisticsResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", example = "1", required = true)
    private Long id;

    /**
     * 服务名
     */
    @ApiModelProperty(value = "服务名", example = "yxt-safe", required = true)
    private String applicationName;

    /**
     * 实际请求path路径，只有一个值
     */
    @ApiModelProperty(value = "实际请求path路径", example = "api/test", required = true)
    private String apiPath;

    /**
     * 实际请求方式，只有一个值
     */
    @ApiModelProperty(value = "实际请求方式", example = "POST", required = true)
    private String apiMethod;

    /**
     * 实际调用网关 C-小程序网关 API-对外网关  BIGDATA-大数据网关 BUS-B端用户网关
     */
    @ApiModelProperty(value = "实际调用网关 C-小程序网关 API-对外网关  BIGDATA-大数据网关 BUS-B端用户网关", example = "小程序网关", required = true)
    private String gateway;

    /**
     * 调用的appKey
     */
    @ApiModelProperty(value = "调用的appKey", example = "3423442", required = true)
    private String appKey;

    /**
     * 调用的鉴权模式 SESSION-会话 SIGN-验签 为空不鉴权
     */
    @ApiModelProperty(value = "调用的鉴权模式 SESSION-会话 SIGN-验签 为空不鉴权", example = "验签", required = true)
    private String authMode;

    /**
     * app的鉴权方式
     */
    @ApiModelProperty(value = "app的鉴权方式", example = "RSA", required = true)
    private String appAuthMethod;

    /**
     * 总调用次数
     */
    @ApiModelProperty(value = "总调用次数", example = "1", required = true)
    private Integer totalCalls;

    /**
     * 调用渠道不匹配拦截次数
     */
    @ApiModelProperty(value = "调用渠道不匹配拦截次数", example = "1", required = true)
    private Integer channelInterceptions;

    /**
     * rbac鉴权拦截次数
     */
    @ApiModelProperty(value = "rbac鉴权拦截次数", example = "1", required = true)
    private Integer rbacInterceptions;

    /**
     * 认证鉴权拦截次数
     */
    @ApiModelProperty(value = "认证鉴权拦截次数", example = "1", required = true)
    private Integer authInterceptions;

    /**
     * 统计时间段
     */
    @ApiModelProperty(value = "统计时间段", example = "2025-01-01", required = true)
    private String timePeriod;

    /**
     * 统计时段开始时间
     */
    @ApiModelProperty(value = "统计时段开始时间", example = "1", required = true)
    private LocalDateTime startTime;

    /**
     * 统计时段结束时间
     */
    @ApiModelProperty(value = "统计时段结束时间", example = "1", required = true)
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "1", required = true)
    private LocalDateTime createdTime;

    public String getGatewayDesc() {
        return AuthGatewayEnum.getDescByName(gateway);
    }

    public String getAuthModeDesc() {
        return AuthModeEnum.getDescByName(authMode);
    }

    public String getAppAuthMethodDesc() {
        return AuthMethodEnum.getDescByName(appAuthMethod);
    }
}
