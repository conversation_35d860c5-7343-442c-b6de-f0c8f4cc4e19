//package com.yxt.safecenter.common.model.enums;
//
///**
// * 密钥类型
// * Since: 2025/2/7 18:09
// * Author: qs
// */
//public enum CerTypeEnum implements BaseEnum {
//    /**
//     *
//     */
//    SIGN("签名"),
//    ENC("加密"),
//    SIGN_ENC("签名加密"),
//    ;
//    private final String desc;
//
//    CerTypeEnum(String desc) {
//        this.desc = desc;
//    }
//
//    public static String getDescByName(String name) {
//        if (name == null) {
//            return null;
//        }
//        try {
//            return valueOf(name).desc;
//        } catch (Exception ignored) {
//
//        }
//        return null;
//    }
//
//    @Override
//    public String getName() {
//        return name();
//    }
//
//    @Override
//    public String getDesc() {
//        return desc;
//    }
//
//    @Override
//    public String getCode() {
//        return name();
//    }
//}
