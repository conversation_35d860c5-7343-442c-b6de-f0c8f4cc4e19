package com.yxt.safecenter.common.model.enums;

/**
 * 请求方式
 * Since: 2025/2/7 18:13
 * Author: qs
 */
public enum ApiWayEnum implements BaseEnum {

    /**
     *
     */
    GET,
    POST,
    DELETE,
    PUT,
    ;

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String getDesc() {
        return name();
    }

    @Override
    public String getCode() {
        return name();
    }
}
