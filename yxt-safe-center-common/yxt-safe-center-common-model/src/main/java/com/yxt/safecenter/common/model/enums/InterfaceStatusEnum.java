package com.yxt.safecenter.common.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * Since: 2025/2/7 18:23
 * Author: qs
 */
public enum InterfaceStatusEnum implements BaseEnum {

    /**
     *
     */
    OFFLINE("已下线"),
    ONLINE("已上线"),
    ONLINE_RUNNING("上线中"),
    OFFLINE_RUNNING("下线中"),
    ;
    private final String desc;

    InterfaceStatusEnum(String desc) {
        this.desc = desc;
    }

    public static String getDescByName(String name) {
        if (name == null) {
            return null;
        }
        try {
            return valueOf(name).desc;
        } catch (Exception ignored) {

        }
        return null;
    }

    public static InterfaceStatusEnum getByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        return Arrays.stream(InterfaceStatusEnum.values())
                .filter(e -> e.name().equals(name))
                .findFirst()
                .orElse(null);
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getCode() {
        return name();
    }
}
