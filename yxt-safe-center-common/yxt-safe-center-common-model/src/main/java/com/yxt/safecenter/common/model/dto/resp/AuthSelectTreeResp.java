package com.yxt.safecenter.common.model.dto.resp;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public class AuthSelectTreeResp {

    private final String code;
    private final String desc;
    private final List<AuthModeResp> authModes;

    @Getter
    @AllArgsConstructor
    public static class AuthModeResp {
        private final String code;
        private final String desc;
        private final List<RbacValidationResp> rbacValidations;

        @Getter
        @AllArgsConstructor
        public static class RbacValidationResp {
            private final String code;
            private final String desc;
        }
    }

}
