package com.yxt.safecenter.common.model.dto.req;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Set;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
public class SafeAppAuthInterfaceUpReq {

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    @NotEmpty(message = "应用id 不能为空")
    private String appKey;


    @ApiModelProperty(value = "添加的应用id")
    private Set<Long> addInterfaceIds;
    @ApiModelProperty(value = "删除的应用id")
    private Set<Long> delInterfaceIds;
}
