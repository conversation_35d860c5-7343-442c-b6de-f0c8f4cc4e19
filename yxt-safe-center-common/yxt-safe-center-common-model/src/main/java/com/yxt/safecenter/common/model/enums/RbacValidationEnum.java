package com.yxt.safecenter.common.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * RBAC权限
 * Since: 2025/2/7 18:20
 * Author: qs
 */
public enum RbacValidationEnum implements BaseEnum {
    /**
     *
     */
    INTERFACE("接口校验"),
    DATA("数据统一处理"),
    ALL("全部"),
    ;
    private final String desc;

    RbacValidationEnum(String desc) {
        this.desc = desc;
    }

    public static String getDescByName(String name) {
        if (name == null) {
            return null;
        }
        try {
            return valueOf(name).desc;
        } catch (Exception ignored) {

        }
        return null;
    }

    public static RbacValidationEnum getByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(e -> e.name().equals(name))
                .findFirst()
                .orElse(null);
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getCode() {
        return name();
    }
}
