package com.yxt.safecenter.common.model;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.yxt.safecenter.common.model.bo.LoginInfoBO;

/**
 * <AUTHOR>
 */
public class UserContext {

    private static final TransmittableThreadLocal<LoginInfoBO> context = new TransmittableThreadLocal<>();

    /**
     * 设置上下文信息
     *
     * @param value 值
     */
    public static void setContext(LoginInfoBO value) {
        context.set(value);
    }

    /**
     * 获取上下文信息
     *
     * @return 值
     */
    public static LoginInfoBO getContext() {
        return context.get();
    }

    /**
     * 清除上下文信息
     */
    public static void clearContext() {
        context.remove();
    }
}