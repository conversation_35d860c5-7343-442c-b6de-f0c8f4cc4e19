package com.yxt.safecenter.common.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 鉴权方式
 * Since: 2025/2/7 18:11
 * Author: qs
 */
public enum AuthMethodEnum implements BaseEnum {
    /**
     *
     */
    RSA_AUTH("RAS/RSA2简单签名"),
    RSA_AUTH_V2("RAS/RSA2复杂签名（推荐）"),
    MD5("MD5简单签名"),
    RSA_AUTH_OLD("RAS/RSA2简单+复杂签名（不建议）"),
    ;
    private final String desc;

    AuthMethodEnum(String desc) {
        this.desc = desc;
    }


    public static String getDescByName(String name) {
        if (name == null) {
            return null;
        }
        try {
            return valueOf(name).desc;
        } catch (Exception ignored) {

        }
        return null;
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getCode() {
        return name();
    }


    public static AuthMethodEnum getByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        return Arrays.stream(AuthMethodEnum.values())
                .filter(e -> e.name().equals(name))
                .findFirst()
                .orElse(null);
    }

    public Boolean isRsa() {
        return name().startsWith("RSA");
    }

}
