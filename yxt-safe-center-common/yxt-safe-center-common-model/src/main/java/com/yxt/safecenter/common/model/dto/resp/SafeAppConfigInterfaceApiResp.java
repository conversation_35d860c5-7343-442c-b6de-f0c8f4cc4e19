package com.yxt.safecenter.common.model.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.safecenter.common.model.enums.AuthMethodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Since: 2025/2/25 13:55
 * Author: qs
 */

@Data
@ApiModel(description = "应用配置、接口响应信息")
public class SafeAppConfigInterfaceApiResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    private String appKey;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String appName;

    /**
     * 私钥
     */
    @ApiModelProperty(value = "私钥")
    private String privateKey;

    /**
     * 公钥
     */
    @ApiModelProperty(value = "公钥")
    private String publicKey;

    /**
     * 使用非对称方式鉴权密钥、TOKEN等
     */
    @ApiModelProperty(value = "使用非对称方式鉴权密钥、TOKEN等")
    private String cerKey;

    /**
     * 鉴权方式 RSA-rsa/rsa2验签 MD5-md5验签 TOKENBS-验证token是否相同的特殊方式
     */
    @ApiModelProperty(value = "鉴权方式 RSA-rsa/rsa2验签 MD5-md5验签 TOKENBS-验证token是否相同的特殊方式")
    private String authMethod;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 失效时间
     */
    @ApiModelProperty(value = "失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "应用授权接口ID列表")
    List<Long> interfaceList;

    public String getAuthMethodDesc() {
        return AuthMethodEnum.getDescByName(authMethod);
    }
}
