package com.yxt.safecenter.common.model.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yxt.safecenter.common.model.dto.SchemaDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Schema转换工具类
 * 用于将JSON字符串转换为SchemaDTO对象
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
public class SchemaConvertUtil {

    public static void main(String[] args) {
        Map<String, SchemaDTO> stringSchemaDTOMap = convertJsonToSchemaMap("{\"request\": {\"in\": \"body\", \"type\": \"object\", \"properties\": {\"scene\": {\"type\": \"string\", \"required\": false, \"description\": \"场景\"}, \"device\": {\"type\": \"string\", \"required\": false, \"description\": \"端\"}, \"bizCode\": {\"type\": \"string\", \"required\": true, \"description\": \"业务身份\"}, \"channel\": {\"type\": \"string\", \"required\": false, \"description\": \"渠道\"}, \"authMode\": {\"enums\": [\"SESSION_B\", \"SESSION_C\", \"SESSION_C_WEAK\", \"APP\", \"NO\"], \"type\": \"string\", \"required\": false, \"description\": \"鉴权模式\"}, \"demoReqs\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"required\": false, \"description\": \"\"}, \"requestId\": {\"type\": \"string\", \"required\": true, \"description\": \"请求编号\"}, \"diseaseDictId\": {\"type\": \"integer\", \"required\": true, \"description\": \"词典id\"}}, \"description\": \"OpenApi-会员积分扣减/返还参数\"}, \"authMode\": {\"in\": \"query\", \"enums\": [\"SESSION_B\", \"SESSION_C\", \"SESSION_C_WEAK\", \"APP\", \"NO\"], \"type\": \"string\", \"required\": true, \"description\": \"\"}, \"headerAuthMode\": {\"in\": \"header\", \"enums\": [\"SESSION_B\", \"SESSION_C\", \"SESSION_C_WEAK\", \"APP\", \"NO\"], \"type\": \"string\", \"required\": false, \"description\": \"\"}}");
        Map<String, Map<String, SchemaDTO>> stringMapMap = convertJsonToGroupedSchema("{\"request\": {\"in\": \"body\", \"type\": \"object\", \"properties\": {\"scene\": {\"type\": \"string\", \"required\": false, \"description\": \"场景\"}, \"device\": {\"type\": \"string\", \"required\": false, \"description\": \"端\"}, \"bizCode\": {\"type\": \"string\", \"required\": true, \"description\": \"业务身份\"}, \"channel\": {\"type\": \"string\", \"required\": false, \"description\": \"渠道\"}, \"authMode\": {\"enums\": [\"SESSION_B\", \"SESSION_C\", \"SESSION_C_WEAK\", \"APP\", \"NO\"], \"type\": \"string\", \"required\": false, \"description\": \"鉴权模式\"}, \"demoReqs\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"required\": false, \"description\": \"\"}, \"requestId\": {\"type\": \"string\", \"required\": true, \"description\": \"请求编号\"}, \"diseaseDictId\": {\"type\": \"integer\", \"required\": true, \"description\": \"词典id\"}}, \"description\": \"OpenApi-会员积分扣减/返还参数\"}, \"authMode\": {\"in\": \"query\", \"enums\": [\"SESSION_B\", \"SESSION_C\", \"SESSION_C_WEAK\", \"APP\", \"NO\"], \"type\": \"string\", \"required\": true, \"description\": \"\"}, \"headerAuthMode\": {\"in\": \"header\", \"enums\": [\"SESSION_B\", \"SESSION_C\", \"SESSION_C_WEAK\", \"APP\", \"NO\"], \"type\": \"string\", \"required\": false, \"description\": \"\"}}");
        Map<String, SchemaDTO> res = convertJsonToSchemaMap("{\"resp\": {\"type\": \"object\", \"properties\": {\"msg\": {\"type\": \"string\", \"required\": false, \"description\": \"请求返回信息\"}, \"code\": {\"type\": \"string\", \"required\": false, \"description\": \"请求返回码，10000-成功，其他失败\"}, \"data\": {\"type\": \"string\", \"required\": false, \"description\": \"请求返回实体对象\"}, \"subCode\": {\"type\": \"string\", \"required\": false, \"description\": \"子请求返回码\"}, \"traceId\": {\"type\": \"string\", \"required\": false, \"description\": \"链路id\"}, \"timestamp\": {\"type\": \"integer\", \"required\": false, \"description\": \"\"}, \"subMessage\": {\"type\": \"string\", \"required\": false, \"description\": \"子响应消息\"}}}}");

    }
    /**
     * 将JSON字符串转换为SchemaDTO Map
     * 
     * @param jsonStr JSON字符串
     * @return Map<String, SchemaDTO> key为参数名，value为SchemaDTO
     */
    public static Map<String, SchemaDTO> convertJsonToSchemaMap(String jsonStr) {
        if (StringUtils.isEmpty(jsonStr)) {
            return new HashMap<>();
        }
        
        try {
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            Map<String, SchemaDTO> schemaMap = new HashMap<>();
            
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                String paramName = entry.getKey();
                Object paramValue = entry.getValue();
                
                if (paramValue instanceof JSONObject) {
                    SchemaDTO schemaDTO = convertJsonObjectToSchema((JSONObject) paramValue);
                    schemaMap.put(paramName, schemaDTO);
                }
            }
            
            return schemaMap;
        } catch (Exception e) {
            throw new RuntimeException("JSON转换为SchemaDTO失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将单个JSON字符串转换为SchemaDTO
     * 
     * @param jsonStr JSON字符串
     * @return SchemaDTO
     */
    public static SchemaDTO convertJsonToSchema(String jsonStr) {
        if (StringUtils.isEmpty(jsonStr)) {
            return new SchemaDTO();
        }
        
        try {
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            return convertJsonObjectToSchema(jsonObject);
        } catch (Exception e) {
            throw new RuntimeException("JSON转换为SchemaDTO失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将JSONObject转换为SchemaDTO
     * 
     * @param jsonObject JSONObject
     * @return SchemaDTO
     */
    private static SchemaDTO convertJsonObjectToSchema(JSONObject jsonObject) {
        SchemaDTO schemaDTO = new SchemaDTO();
        
        // 设置基本属性
        schemaDTO.setType(jsonObject.getString("type"));
        schemaDTO.setIn(jsonObject.getString("in"));
        schemaDTO.setRequired(jsonObject.getBoolean("required"));
        schemaDTO.setDescription(jsonObject.getString("description"));
        
        // 处理枚举值
        if (jsonObject.containsKey("enums")) {
            List<String> enumList = jsonObject.getJSONArray("enums").toJavaList(String.class);
            schemaDTO.setEnums(enumList);
        }
        
        // 处理对象属性
        if (jsonObject.containsKey("properties")) {
            JSONObject propertiesJson = jsonObject.getJSONObject("properties");
            Map<String, SchemaDTO> properties = new HashMap<>();

            for (Map.Entry<String, Object> entry : propertiesJson.entrySet()) {
                String propertyName = entry.getKey();
                Object propertyValue = entry.getValue();

                if (propertyValue instanceof JSONObject) {
                    SchemaDTO propertySchema = convertJsonObjectToSchema((JSONObject) propertyValue);
                    properties.put(propertyName, propertySchema);
                }
            }

            schemaDTO.setProperties(properties);
        }
        
        // 处理数组元素
        if (jsonObject.containsKey("items")) {
            JSONObject itemsJson = jsonObject.getJSONObject("items");
            SchemaDTO itemsSchema = convertJsonObjectToSchema(itemsJson);
            schemaDTO.setItems(itemsSchema);
        }
        
        return schemaDTO;
    }

    /**
     * 将SchemaDTO转换为JSON字符串
     * 
     * @param schemaDTO SchemaDTO对象
     * @return JSON字符串
     */
    public static String convertSchemaToJson(SchemaDTO schemaDTO) {
        if (schemaDTO == null) {
            return "{}";
        }
        
        try {
            return JSON.toJSONString(schemaDTO);
        } catch (Exception e) {
            throw new RuntimeException("SchemaDTO转换为JSON失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将SchemaDTO Map转换为JSON字符串
     *
     * @param schemaMap SchemaDTO Map
     * @return JSON字符串
     */
    public static String convertSchemaMapToJson(Map<String, SchemaDTO> schemaMap) {
        if (schemaMap == null || schemaMap.isEmpty()) {
            return "{}";
        }

        try {
            return JSON.toJSONString(schemaMap);
        } catch (Exception e) {
            throw new RuntimeException("SchemaDTO Map转换为JSON失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将参数按照in字段分组，并处理对象类型参数的展开
     *
     * @param schemaMap 原始的SchemaDTO Map
     * @return Map<String, Map<String, SchemaDTO>> 外层key为in的值(query、header、path、body)，内层key为参数名
     */
    public static Map<String, Map<String, SchemaDTO>> convertToGroupedSchema(Map<String, SchemaDTO> schemaMap) {
        if (schemaMap == null || schemaMap.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, Map<String, SchemaDTO>> groupedSchema = new HashMap<>();

        for (Map.Entry<String, SchemaDTO> entry : schemaMap.entrySet()) {
            String paramName = entry.getKey();
            SchemaDTO schema = entry.getValue();
            String inValue = schema.getIn();

            if (StringUtils.isEmpty(inValue)) {
                continue;
            }

            // 确保分组存在
            groupedSchema.computeIfAbsent(inValue, k -> new HashMap<>());

            // 对于所有类型的对象参数，都需要展开其属性以满足Spring MVC接收参数的形式
            if ("object".equals(schema.getType()) && schema.getProperties() != null) {

                // 展开对象属性到外层
                expandObjectProperties(groupedSchema.get(inValue), schema.getProperties(), inValue);

            } else {
                // 直接添加参数
                groupedSchema.get(inValue).put(paramName, schema);
            }
        }

        return groupedSchema;
    }

    /**
     * 展开对象属性到指定的参数组中
     *
     * @param targetGroup 目标参数组
     * @param properties 对象属性
     * @param inValue 参数位置
     */
    private static void expandObjectProperties(Map<String, SchemaDTO> targetGroup,
                                             Map<String, SchemaDTO> properties,
                                             String inValue) {
        for (Map.Entry<String, SchemaDTO> propEntry : properties.entrySet()) {
            String propName = propEntry.getKey();
            SchemaDTO propSchema = propEntry.getValue();

            // 创建新的SchemaDTO，设置正确的in值
            SchemaDTO expandedSchema = new SchemaDTO();
            expandedSchema.setType(propSchema.getType());
            expandedSchema.setIn(inValue);
            expandedSchema.setRequired(propSchema.getRequired());
            expandedSchema.setDescription(propSchema.getDescription());
            expandedSchema.setEnums(propSchema.getEnums());
            expandedSchema.setItems(propSchema.getItems());

            // 如果属性本身也是对象类型，递归展开
            if ("object".equals(propSchema.getType()) && propSchema.getProperties() != null) {
                expandObjectProperties(targetGroup, propSchema.getProperties(), inValue);
            } else {
                targetGroup.put(propName, expandedSchema);
            }
        }
    }

    /**
     * 将JSON字符串转换为按in分组的SchemaDTO结构
     *
     * @param jsonStr JSON字符串
     * @return Map<String, Map<String, SchemaDTO>> 按in分组的结构
     */
    public static Map<String, Map<String, SchemaDTO>> convertJsonToGroupedSchema(String jsonStr) {
        Map<String, SchemaDTO> schemaMap = convertJsonToSchemaMap(jsonStr);
        return convertToGroupedSchema(schemaMap);
    }

    /**
     * 将按in分组的SchemaDTO结构转换为JSON字符串
     *
     * @param groupedSchema 按in分组的SchemaDTO结构
     * @return JSON字符串
     */
    public static String convertGroupedSchemaToJson(Map<String, Map<String, SchemaDTO>> groupedSchema) {
        if (groupedSchema == null || groupedSchema.isEmpty()) {
            return "{}";
        }

        try {
            return JSON.toJSONString(groupedSchema);
        } catch (Exception e) {
            throw new RuntimeException("分组SchemaDTO转换为JSON失败: " + e.getMessage(), e);
        }
    }
}
