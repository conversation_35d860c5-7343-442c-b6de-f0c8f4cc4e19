package com.yxt.safecenter.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 参数Schema DTO
 * 用于描述API参数的结构信息，支持JSON Schema格式
 *
 * Since: 2025/07/30 11:35
 * Author: qs
 */
@ApiModel("参数Schema信息")
@Data
public class SchemaDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参数类型：string、integer、number、boolean、object、array
     */
    @ApiModelProperty(value = "参数类型", example = "string", required = true)
    private String type;

    /**
     * 参数位置：header、query、body、path（仅请求参数有此字段）
     */
    @ApiModelProperty(value = "参数位置", example = "query")
    private String in;

    /**
     * 是否必须
     */
    @ApiModelProperty(value = "是否必须", example = "true")
    private Boolean required;

    /**
     * 参数描述
     */
    @ApiModelProperty(value = "参数描述", example = "用户名称")
    private String description;

    /**
     * 枚举值列表（当type为string且参数为枚举类型时）
     */
    @ApiModelProperty(value = "枚举值列表", example = "[\"ONLINE\", \"OFFLINE\"]")
    private List<String> enums;

    /**
     * 对象属性（当type为object时）
     * key为属性名，value为属性的SchemaDTO
     */
    @ApiModelProperty(value = "对象属性")
    private Map<String, SchemaDTO> properties;

    /**
     * 数组元素类型（当type为array时）
     */
    @ApiModelProperty(value = "数组元素类型")
    private SchemaDTO items;

}
