package com.yxt.safecenter.common.model.dto.req;


import com.yxt.safecenter.common.model.dto.AuthInfoDTO;
import com.yxt.safecenter.common.model.enums.EncryptionRequiredEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
public class SafeInterfaceUpReq {

    @ApiModelProperty(value = "主键", example = "1", required = true)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @ApiModelProperty(value = "接口描述", example = "获取用户信息", required = true)
    private String apiDesc;

    /**
     * 权限相关 json
     */
    @ApiModelProperty(value = "权限相关", example = "[{},{}]")
    private List<AuthInfoDTO> authInfo;


    @ApiModelProperty(value = "是否统一加解密  ENC(\"加密\"),\n" +
            "    DENC(\"解密\"),\n" +
            "    ENC_DENC(\"加密解密\"),")
    private EncryptionRequiredEnum encryptionRequired;
}
