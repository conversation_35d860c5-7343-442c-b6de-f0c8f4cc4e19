package com.yxt.safecenter.common.model.dto.req;

import com.yxt.safecenter.common.model.dto.AuthInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Since: 2025/05/07 15:16
 * Author: qs
 */

@ApiModel
@Data
public class SetAuthInfoReq {

    @ApiModelProperty(value = "应用名称", required = true)
    @NotEmpty(message = "应用名称不能为空")
    private String applicationName;

    @ApiModelProperty(value = "鉴权信息", required = true)
    @NotNull(message = "鉴权信息不能为空")
    private List<AuthInfoDTO> authInfos;

    @ApiModelProperty(value = "接口配置路径", required = true)
    @NotNull(message = "接口配置路径不能为空")
    private List<String> pathPatternList;
}
