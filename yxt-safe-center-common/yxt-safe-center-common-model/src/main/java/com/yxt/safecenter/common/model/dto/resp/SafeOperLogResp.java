package com.yxt.safecenter.common.model.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 操作日志记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Data
@ApiModel(description = "操作日志记录")
public class SafeOperLogResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日志主键", example = "1", required = true)
    private Long id;

    @ApiModelProperty(value = "模块标题", example = "用户管理", required = true)
    private String title;

    @ApiModelProperty(value = "业务类型（0: 其它, 1: 新增, 2: 修改, 3: 删除）", example = "1", required = true)
    private Integer businessType;

    @ApiModelProperty(value = "方法名称", example = "com.example.controller.UserController.addUser", required = true)
    private String method;

    @ApiModelProperty(value = "请求方式（GET/POST/PUT/DELETE等）", example = "POST", required = true)
    private String requestMethod;

    @ApiModelProperty(value = "操作类别（0: 其它, 1: 后台用户, 2: 手机端用户）", example = "1", required = true)
    private Integer operatorType;

    @ApiModelProperty(value = "操作人员", example = "admin", required = true)
    private String operName;

    @ApiModelProperty(value = "操作人员ID", example = "12345", required = true)
    private String operId;

    @ApiModelProperty(value = "请求URL", example = "/api/user/add", required = true)
    private String operUrl;

    @ApiModelProperty(value = "主机地址", example = "***********", required = true)
    private String operIp;

    @ApiModelProperty(value = "操作地点", example = "北京", required = true)
    private String operLocation;

    @ApiModelProperty(value = "请求参数", example = "{\"username\":\"test\",\"password\":\"123\"}", required = true)
    private String operParam;

    @ApiModelProperty(value = "返回参数", example = "{\"id\":1,\"username\":\"test\"}", required = true)
    private String jsonResult;

    @ApiModelProperty(value = "操作状态（0: 正常, 1: 异常）", example = "0", required = true)
    private Integer status;

    @ApiModelProperty(value = "错误消息", example = "null")
    private String errorMsg;

    @ApiModelProperty(value = "操作时间", example = "2025-02-18T16:18:01.441", required = true)
    private LocalDateTime operTime;

    @ApiModelProperty(value = "消耗时间（毫秒）", example = "123")
    private Long costTime;
}