package com.yxt.safecenter.common.model.dto.req;


import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
public class SafeAppConfigQueryReq extends PageBase {

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    private String appKey;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String appName;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Boolean enable;
}
