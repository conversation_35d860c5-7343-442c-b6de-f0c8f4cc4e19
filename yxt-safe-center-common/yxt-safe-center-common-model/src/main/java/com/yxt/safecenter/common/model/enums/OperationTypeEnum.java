package com.yxt.safecenter.common.model.enums;

/**
 * 操作类型
 * Since: 2025/2/7 18:26
 * Author: qs
 */
public enum OperationTypeEnum implements BaseEnum {
    /**
     *
     */
    ADD("新增"),
    UPDATE("修改"),
    DELETE("删除"),
    ;
    private final String desc;

    OperationTypeEnum(String desc) {
        this.desc = desc;
    }

    public static String getDescByName(String name) {
        if (name == null) {
            return null;
        }
        try {
            return valueOf(name).desc;
        } catch (Exception ignored) {

        }
        return null;
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getCode() {
        return name();
    }
}
