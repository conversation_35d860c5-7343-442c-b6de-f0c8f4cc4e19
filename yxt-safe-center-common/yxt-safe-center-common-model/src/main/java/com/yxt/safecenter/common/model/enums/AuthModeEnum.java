package com.yxt.safecenter.common.model.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 接口鉴权模式
 * Since: 2025/2/7 18:16
 * Author: qs
 */
public enum AuthModeEnum implements BaseEnum {
    /**
     *
     */
    SESSION_B("B端用户Token鉴权", Arrays.asList(RbacValidationEnum.INTERFACE, RbacValidationEnum.DATA, RbacValidationEnum.ALL)),
    SESSION_C("C端用户Token鉴权", Collections.emptyList()),
    SESSION_C_WEAK("C端用户弱鉴权", Collections.emptyList()),
    APP("应用鉴权", Collections.emptyList()),
    NO("不鉴权", Collections.emptyList()),
    ;
    private final String desc;
    @Getter
    private final List<RbacValidationEnum> rbacValidations;

    AuthModeEnum(String desc, List<RbacValidationEnum> rbacValidations) {
        this.desc = desc;
        this.rbacValidations = rbacValidations;
    }


    public static String getDescByName(String name) {
        if (name == null) {
            return null;
        }
        try {
            return valueOf(name).desc;
        } catch (Exception ignored) {

        }
        return null;
    }

    public static AuthModeEnum getByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        return Arrays.stream(AuthModeEnum.values())
                .filter(e -> e.name().equals(name))
                .findFirst()
                .orElse(null);
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getCode() {
        return name();
    }
}
