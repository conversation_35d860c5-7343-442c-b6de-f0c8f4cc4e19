package com.yxt.safecenter.common.lib.config;

import com.yxt.common.wechatrobot.enums.IRobotConfig;

public enum SafeCenterRobotConfig implements IRobotConfig {

    AUTH_ALERT(
            "dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ef44e7b0-ccf1-4a0a-addb-51916b5d15ba;" +
                    "test|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ef44e7b0-ccf1-4a0a-addb-51916b5d15ba;" +
                    "pre|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=42c5fd88-c1d4-4137-aec9-2cce7f85e161;" +
                    "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=42c5fd88-c1d4-4137-aec9-2cce7f85e161;"
            ,"迁移告警"),
    ;

    private final String content;
    private final String webHook;

    @Override
    public String getWebhook() {
        return webHook;
    }

    @Override
    public String getContent() {
        return content;
    }

    SafeCenterRobotConfig(String webHook, String content) {
        this.content = content;
        this.webHook = webHook;
    }

}
