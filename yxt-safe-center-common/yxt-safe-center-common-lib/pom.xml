<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-safe-center-common-lib</artifactId>
    <!--    <version>${reversion}</version>-->
    <name>yxt-safe-center-common-lib</name>
    <description>yxt-safe-center-common-lib</description>

    <parent>
        <groupId>com.yxt.safecenter</groupId>
        <artifactId>yxt-safe-center-common</artifactId>
        <version>${reversion}</version>
    </parent>

    <properties>
        <module.deploy.skip>true</module.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-lang</artifactId>
        </dependency>
        <!-- 告警 -->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-alarm</artifactId>
        </dependency>
        <!-- 报警机器人 -->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-wechatrobot</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-xml</artifactId>
        </dependency>

        <!--todo common-auth引入报错-->
        <!--        <dependency>-->
        <!--            <groupId>com.yxt</groupId>-->
        <!--            <artifactId>yxt-common-auth</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-sign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-redis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
    </dependencies>

</project>
