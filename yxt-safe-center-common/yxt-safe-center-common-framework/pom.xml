<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-safe-center-common-framework</artifactId>
    <!--    <version>${reversion}</version>-->
    <name>yxt-safe-center-common-framework</name>
    <description>yxt-safe-center-common-framework</description>

    <parent>
        <groupId>com.yxt.safecenter</groupId>
        <artifactId>yxt-safe-center-common</artifactId>
        <version>${reversion}</version>
    </parent>

    <properties>
        <module.deploy.skip>true</module.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yxt.safecenter</groupId>
            <artifactId>yxt-safe-center-common-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.safecenter</groupId>
            <artifactId>yxt-safe-center-common-lib</artifactId>
        </dependency>
        <!-- SpringBoot Web容器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- SpringBoot 拦截器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>eu.bitwalker</groupId>
            <artifactId>UserAgentUtils</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>
