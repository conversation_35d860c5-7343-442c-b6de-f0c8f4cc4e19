package com.yxt.safecenter.common.framework.log.model.enums;

import com.yxt.safecenter.common.model.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 操作人类别
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum OperatorType implements BaseEnum {
    /**
     * 其它
     */
    OTHER(0, "其它"),
    /**
     * 后台用户
     */
    MANAGE(1, "后台用户"),
    /**
     * 手机端用户
     */
    MOBILE(2, "手机端用户");

    private Integer code;
    private String desc;

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
