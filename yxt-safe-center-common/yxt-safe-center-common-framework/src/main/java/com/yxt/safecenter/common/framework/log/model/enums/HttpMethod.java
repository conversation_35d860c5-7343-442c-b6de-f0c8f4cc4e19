package com.yxt.safecenter.common.framework.log.model.enums;

import com.yxt.safecenter.common.model.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import java.util.HashMap;
import java.util.Map;

/**
 * 请求方式
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum HttpMethod implements BaseEnum {
    /**
     *
     */
    GET(0, "GET 请求"),
    HEAD(1, "HEAD 请求"),
    POST(2, "POST 请求"),
    PUT(3, "PUT 请求"),
    PATCH(4, "PATCH 请求"),
    DELETE(5, "DELETE 请求"),
    OPTIONS(6, "OPTIONS 请求"),
    TRACE(7, "TRACE 请求");

    private Integer code;
    private String desc;

    private static final Map<String, HttpMethod> mappings = new HashMap<>(16);

    static {
        for (HttpMethod httpMethod : values()) {
            mappings.put(httpMethod.name(), httpMethod);
        }
    }

    @Nullable
    public static HttpMethod resolve(@Nullable String method) {
        return (method != null ? mappings.get(method) : null);
    }

    public boolean matches(String method) {
        return (this == resolve(method));
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
