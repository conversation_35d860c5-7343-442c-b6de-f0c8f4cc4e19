package com.yxt.safecenter.common.framework.log.model.enums;

import com.yxt.safecenter.common.model.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 业务操作类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum BusinessType implements BaseEnum {
    /**
     *
     */
    OTHER(0, "其它"),
    INSERT(1, "新增"),
    UPDATE(2, "修改"),
    DELETE(3, "修改"),
    EXPORT(4, "导出"),
    IMPORT(5, "导入"),
    CLEAN(6, "清空数据"),

    ;
    private Integer code;
    private String desc;


    @Override
    public String getName() {
        return name();
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

}
