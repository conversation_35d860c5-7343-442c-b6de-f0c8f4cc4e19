package com.yxt.safecenter.common.framework.core.interceptor;

import com.yxt.safecenter.common.model.UserContext;
import com.yxt.safecenter.common.model.bo.LoginInfoBO;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class UserContextInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String userId = request.getHeader("userId");
        String userName = request.getHeader("userName");
        UserContext.setContext(new LoginInfoBO(userId, userName));
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清理TTL中的数据
        UserContext.clearContext();
    }
}
    