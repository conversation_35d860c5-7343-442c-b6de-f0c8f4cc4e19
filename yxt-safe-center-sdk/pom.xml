<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-safe-center-sdk</artifactId>
    <name>yxt-safe-center-sdk</name>
    <description>yxt-safe-center-sdk</description>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.yxt.safecenter</groupId>
        <artifactId>yxt-safe-center</artifactId>
        <version>${reversion}</version>

    </parent>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>

        <module.deploy.skip>false</module.deploy.skip>
    </properties>

    <modules>
        <module>yxt-safe-center-feign-sdk</module>
        <module>yxt-safe-center-auth-sdk</module>
        <module>yxt-safe-center-online-sdk</module>
    </modules>

    <dependencies>
        <!-- common-lang -->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-lang</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>


</project>
