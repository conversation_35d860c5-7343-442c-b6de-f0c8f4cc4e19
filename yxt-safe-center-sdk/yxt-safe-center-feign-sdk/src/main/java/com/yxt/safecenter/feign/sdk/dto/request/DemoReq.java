package com.yxt.safecenter.feign.sdk.dto.request;


import com.yxt.lang.dto.api.MiddleRequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("OpenApi-会员积分扣减/返还参数")
@Data
public class DemoReq extends MiddleRequestBase {
        @ApiModelProperty("请求编号")
        private @NotBlank(
                message = "请求编号不能为空"
        ) String requestId;

    @ApiModelProperty("词典id")
    private @NotNull(
            message = "词典id不能为空"
    ) Integer diseaseDictId;

    private List<String> demoReqs;
}
