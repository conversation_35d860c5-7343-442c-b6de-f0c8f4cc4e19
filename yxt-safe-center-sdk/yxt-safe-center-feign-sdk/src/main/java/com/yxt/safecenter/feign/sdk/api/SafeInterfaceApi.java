package com.yxt.safecenter.feign.sdk.api;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.model.constant.CommonConstant;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceApiResp;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import com.yxt.safecenter.feign.sdk.dto.request.ApiInterfaceQueryReq;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * Since: 2025/2/25 11:28
 * Author: qs
 */

@FeignClient(name = CommonConstant.SYSTEM_NAME)
public interface SafeInterfaceApi {

    /**
     * 查询应用所有接口
     * @param applicationName 应用名
     * @return list
     */
    @ApiOperation(value = "查询应用所有接口", notes = "接口")
    @GetMapping(value = "/api/interface/listByApplicationName")
    ResponseBase<List<SafeInterfaceApiResp>> listByApplicationName(@RequestParam String applicationName);

    /**
     * 分页查询接口信息（带项目权限）
     * @param req 请求参数
     * @return page
     */
    @ApiOperation(value = "分页查询接口信息", notes = "分页查询接口信息")
    @PostMapping(value = "/api/interface/page")
    ResponseBase<PageDTO<SafeInterfaceResp>> page(@RequestHeader String empCode, @RequestBody ApiInterfaceQueryReq req);

    /**
     * 根据id查询接口列表（带项目权限）
     * @param ids 请求参数，最大限制200
     * @return page
     */
    @ApiOperation(value = "根据id查询接口列表", notes = "根据id查询接口列表")
    @PostMapping(value = "/api/interface/listByIds")
    ResponseBase<List<SafeInterfaceResp>> listByIds(@RequestHeader String empCode, @RequestBody Collection<Long> ids);

}
