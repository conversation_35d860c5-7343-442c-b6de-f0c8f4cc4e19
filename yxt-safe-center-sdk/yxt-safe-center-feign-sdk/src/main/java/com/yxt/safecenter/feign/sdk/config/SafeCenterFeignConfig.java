package com.yxt.safecenter.feign.sdk.config;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;

import java.util.stream.Collectors;

/**
 * Since: 2025/2/25 19:32
 * Author: qs
 */

@Configuration
@EnableFeignClients(basePackages = "com.yxt.safecenter.feign.sdk.api")
public class SafeCenterFeignConfig {

    /**
     * 解决高版本 cloud-openfeign 中无 HttpMessageConverters bean
     */
    @Bean
    @ConditionalOnMissingBean
    public HttpMessageConverters messageConverters(ObjectProvider<HttpMessageConverter<?>> converters) {
        return new HttpMessageConverters(converters.orderedStream().collect(Collectors.toList()));
    }
}
