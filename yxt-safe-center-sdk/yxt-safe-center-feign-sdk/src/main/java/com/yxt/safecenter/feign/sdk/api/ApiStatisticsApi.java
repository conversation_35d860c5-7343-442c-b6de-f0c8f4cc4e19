package com.yxt.safecenter.feign.sdk.api;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.model.constant.CommonConstant;
import com.yxt.safecenter.common.model.dto.req.ApiCallStatisticsReq;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Since: 2025/2/14 14:42
 * Author: qs
 */

@FeignClient(name = CommonConstant.SYSTEM_NAME)
public interface ApiStatisticsApi {

    @ApiOperation(value = "保存网关接口统计数据", notes = "数据统计")
    @PostMapping(value = "/api/interface/statistics")
    ResponseBase<Void> apiStatisticsSaveBatch(@RequestBody List<ApiCallStatisticsReq> reqList);
}
