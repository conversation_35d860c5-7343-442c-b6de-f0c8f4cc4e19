package com.yxt.safecenter.feign.sdk.dto.request;


import com.yxt.lang.dto.PageBase;
import com.yxt.safecenter.common.model.enums.InterfaceStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
public class ApiInterfaceQueryReq extends PageBase {

    @ApiModelProperty(value = "服务名")
    private String applicationName;

    @ApiModelProperty(value = "API类")
    private String apiClass;

    @ApiModelProperty(value = "API方法", example = "getUser", required = false)
    private String apiMethod;

    @ApiModelProperty(value = "path路径", example = "/users/{id}", required = false)
    private String apiPath;

    @ApiModelProperty(value = "API名称", example = "查询用户信息", required = false)
    private String apiName;

    @ApiModelProperty(value = "api名称/路径", example = "/user", required = false)
    private String searchKey;

    @ApiModelProperty(value = "状态 上线中-ONLINE_RUNNING，已上线-ONLINE，下线中-OFFLINE_RUNNING，已下线-OFFLINE_RUNNING")
    private InterfaceStatusEnum status;
}
