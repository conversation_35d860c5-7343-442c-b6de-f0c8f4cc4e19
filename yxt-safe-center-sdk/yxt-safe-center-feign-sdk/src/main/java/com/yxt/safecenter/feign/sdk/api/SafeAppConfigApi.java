package com.yxt.safecenter.feign.sdk.api;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.model.constant.CommonConstant;
import com.yxt.safecenter.common.model.dto.req.SafeEnableAppConfigInterfacePageReq;
import com.yxt.safecenter.common.model.dto.resp.SafeAppConfigInterfaceApiResp;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Since: 2025/2/25 14:14
 * Author: qs
 */

@FeignClient(name = CommonConstant.SYSTEM_NAME)
public interface SafeAppConfigApi {

    /**
     * 分页查询可用的应用配置
     *
     * @param pageReq 查询参数
     * @return pageDTO
     */
    @ApiOperation(value = "分页查询可用的应用配置", notes = "应用")
    @PostMapping(value = "/api/appConfig/page")
    ResponseBase<PageDTO<SafeAppConfigInterfaceApiResp>> enableAppConfigPage(@RequestBody SafeEnableAppConfigInterfacePageReq pageReq);
}
