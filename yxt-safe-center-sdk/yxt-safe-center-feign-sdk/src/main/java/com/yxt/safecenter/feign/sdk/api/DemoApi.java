package com.yxt.safecenter.feign.sdk.api;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.feign.sdk.dto.request.DemoReq;
import com.yxt.safecenter.feign.sdk.dto.response.DemoResp;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "yxt-middle-chronic")
public interface DemoApi {
    @ApiOperation(value = "demo-通过词典ID查询慢病词典信息", notes = "demo-通过词典ID查询慢病词典信息")
    @PostMapping(value = "/api/demo/getById")
    ResponseBase<DemoResp> getByIdFromMysql(@RequestBody DemoReq demoReq);

    @ApiOperation(value = "demo-通过词典ID查询慢病词典信息,从redis获取", notes = "demo-通过词典ID查询慢病词典信息,从redis获取")
    @PostMapping(value = "/api/demo/getByIdFromRedis")
    ResponseBase<DemoResp> getByIdFromRedis(@RequestBody DemoReq demoReq);

}
