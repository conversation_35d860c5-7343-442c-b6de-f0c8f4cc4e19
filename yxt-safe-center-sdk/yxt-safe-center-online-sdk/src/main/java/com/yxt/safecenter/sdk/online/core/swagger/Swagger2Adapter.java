package com.yxt.safecenter.sdk.online.core.swagger;

import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * Swagger2 适配器实现
 */
public class Swagger2Adapter implements SwaggerAnnotationAdapter {
    
    @Override
    public String getApiOperationValue(Method method) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Annotation> apiOperationClass = (Class<? extends Annotation>) 
                Class.forName("io.swagger.annotations.ApiOperation");
            Annotation annotation = method.getAnnotation(apiOperationClass);
            if (annotation != null) {
                Method valueMethod = apiOperationClass.getMethod("value");
                return (String) valueMethod.invoke(annotation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }

    @Override
    public String getApiOperationNotes(Method method) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Annotation> apiOperationClass = (Class<? extends Annotation>) 
                Class.forName("io.swagger.annotations.ApiOperation");
            Annotation annotation = method.getAnnotation(apiOperationClass);
            if (annotation != null) {
                Method notesMethod = apiOperationClass.getMethod("notes");
                return (String) notesMethod.invoke(annotation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }

    @Override
    public String getApiParamValue(Parameter parameter) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Annotation> apiParamClass = (Class<? extends Annotation>) 
                Class.forName("io.swagger.annotations.ApiParam");
            Annotation annotation = parameter.getAnnotation(apiParamClass);
            if (annotation != null) {
                Method valueMethod = apiParamClass.getMethod("value");
                return (String) valueMethod.invoke(annotation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }

    @Override
    public String getApiModelValue(Class<?> clazz) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Annotation> apiModelClass = (Class<? extends Annotation>) 
                Class.forName("io.swagger.annotations.ApiModel");
            Annotation annotation = clazz.getAnnotation(apiModelClass);
            if (annotation != null) {
                Method valueMethod = apiModelClass.getMethod("value");
                return (String) valueMethod.invoke(annotation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }

    @Override
    public String getApiModelDescription(Class<?> clazz) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Annotation> apiModelClass = (Class<? extends Annotation>) 
                Class.forName("io.swagger.annotations.ApiModel");
            Annotation annotation = clazz.getAnnotation(apiModelClass);
            if (annotation != null) {
                Method descriptionMethod = apiModelClass.getMethod("description");
                return (String) descriptionMethod.invoke(annotation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }

    @Override
    public String getApiModelPropertyValue(Field field) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Annotation> apiModelPropertyClass = (Class<? extends Annotation>) 
                Class.forName("io.swagger.annotations.ApiModelProperty");
            Annotation annotation = field.getAnnotation(apiModelPropertyClass);
            if (annotation != null) {
                Method valueMethod = apiModelPropertyClass.getMethod("value");
                return (String) valueMethod.invoke(annotation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }
}
