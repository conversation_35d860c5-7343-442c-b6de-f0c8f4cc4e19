package com.yxt.safecenter.sdk.online.core.analyzer;

import com.yxt.lang.util.JsonUtils;
import com.yxt.safecenter.sdk.online.core.schema.SchemaGenerator;
import com.yxt.safecenter.sdk.online.core.swagger.SwaggerAnnotationAdapter;
import com.yxt.safecenter.sdk.online.core.util.TypeUtils;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 参数分析器
 * 负责解析Controller方法的请求参数
 */
@Slf4j
@RequiredArgsConstructor
public class ParameterAnalyzer {

    private final SwaggerAnnotationAdapter swaggerAdapter;
    private final SchemaGenerator schemaGenerator;
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    /**
     * 解析请求信息
     */
    public String parseRequestInfo(Method method) {
        try {
            Map<String, Object> requestInfo = new HashMap<>();

            Parameter[] parameters = method.getParameters();
            String[] parameterNames = parameterNameDiscoverer.getParameterNames(method);

            for (int i = 0; i < parameters.length; i++) {
                Parameter parameter = parameters[i];
                String paramName = TypeUtils.getParameterName(parameter, parameterNames, i);

                // 根据注解类型分类参数
                if (parameter.isAnnotationPresent(RequestHeader.class)) {
                    RequestHeader requestHeader = parameter.getAnnotation(RequestHeader.class);
                    String headerName = StringUtils.isNotBlank(requestHeader.value()) ? requestHeader.value() :
                            StringUtils.isNotBlank(requestHeader.name()) ? requestHeader.name() : paramName;
                    Map<String, Object> paramSchema = createParameterSchema(parameter, "header");
                    requestInfo.put(headerName, paramSchema);
                } else if (parameter.isAnnotationPresent(PathVariable.class)) {
                    PathVariable pathVariable = parameter.getAnnotation(PathVariable.class);
                    String pathName = StringUtils.isNotBlank(pathVariable.value()) ? pathVariable.value() :
                            StringUtils.isNotBlank(pathVariable.name()) ? pathVariable.name() : paramName;
                    Map<String, Object> paramSchema = createParameterSchema(parameter, "path");
                    requestInfo.put(pathName, paramSchema);
                } else if (parameter.isAnnotationPresent(RequestParam.class)) {
                    RequestParam requestParam = parameter.getAnnotation(RequestParam.class);
                    String queryName = StringUtils.isNotBlank(requestParam.value()) ? requestParam.value() :
                            StringUtils.isNotBlank(requestParam.name()) ? requestParam.name() : paramName;
                    Map<String, Object> paramSchema = createParameterSchema(parameter, "query");
                    requestInfo.put(queryName, paramSchema);
                } else if (parameter.isAnnotationPresent(RequestBody.class)) {
                    // 对于RequestBody，创建对象schema，使用变量名作为key
                    Map<String, Object> bodySchema = schemaGenerator.createTypeSchema(parameter.getType(), parameter.getParameterizedType());
                    bodySchema.put("in", "body");
                    requestInfo.put(paramName, bodySchema);
                } else {
                    // 没有注解的参数默认作为RequestParam处理
                    Map<String, Object> paramSchema = createParameterSchema(parameter, "query");
                    requestInfo.put(paramName, paramSchema);
                }
            }

            return JsonUtils.toJson(requestInfo);
        } catch (Exception e) {
            log.error("解析请求信息失败", e);
            return "{}";
        }
    }

    /**
     * 创建参数Schema
     */
    private Map<String, Object> createParameterSchema(Parameter parameter, String in) {
        Map<String, Object> schema = new HashMap<>();

        // 获取类型信息
        String type = TypeUtils.getJsonSchemaType(parameter.getType());
        schema.put("type", type);

        // 参数位置
        schema.put("in", in);

        // 是否必须
        boolean required = TypeUtils.isParameterRequired(parameter);
        schema.put("required", required);

        // 参数说明
        String description = getParameterDescription(parameter);
        schema.put("description", description);

        // 如果是枚举类型，添加enum属性
        if ("string".equals(type) && parameter.getType().isEnum()) {
            List<String> enumValues = TypeUtils.getEnumValues(parameter.getType());
            if (!enumValues.isEmpty()) {
                schema.put("enums", enumValues);
            }
        }
        // 如果是对象类型，添加properties
        else if ("object".equals(type)) {
            Map<String, Object> properties = schemaGenerator.createPropertiesFromClass(parameter.getType());
            if (!properties.isEmpty()) {
                schema.put("properties", properties);
            }
        }
        // 如果是数组类型，添加items
        else if ("array".equals(type)) {
            Map<String, Object> items = schemaGenerator.createArrayItemsSchema(parameter.getType(), parameter.getParameterizedType(), null);
            if (!items.isEmpty()) {
                schema.put("items", items);
            }
        }

        return schema;
    }

    /**
     * 获取参数描述
     */
    private String getParameterDescription(Parameter parameter) {
        if (swaggerAdapter != null) {
            String description = swaggerAdapter.getApiParamValue(parameter);
            if (StringUtils.isNotBlank(description)) {
                return description;
            }
        }

        // 兼容原有的直接注解方式（向后兼容）
        if (parameter.isAnnotationPresent(ApiParam.class)) {
            return parameter.getAnnotation(ApiParam.class).value();
        }
        return "";
    }
}
