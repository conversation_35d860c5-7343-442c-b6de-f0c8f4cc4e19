package com.yxt.safecenter.sdk.online;

import com.yxt.safecenter.sdk.online.core.ControllerScanner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * .
 *
 * <AUTHOR>
 * @date 2025年02月20日 16:53
 */
@Configuration
@Slf4j
@Import({ControllerScanner.class})
@EnableFeignClients(basePackages = "com.yxt.safecenter.sdk.online.api")
public class SafeCenterOnlineConfig {
}
