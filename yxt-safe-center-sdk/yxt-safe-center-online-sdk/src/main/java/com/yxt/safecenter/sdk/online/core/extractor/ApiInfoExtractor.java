package com.yxt.safecenter.sdk.online.core.extractor;

import com.yxt.safecenter.sdk.online.core.swagger.SwaggerAnnotationAdapter;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * API信息提取器
 * 负责从Controller方法中提取API相关信息
 */
@Slf4j
@RequiredArgsConstructor
public class ApiInfoExtractor {

    private final SwaggerAnnotationAdapter swaggerAdapter;

    /**
     * 解析接口名称信息
     */
    public String parseApiName(Method method) {
        if (swaggerAdapter != null) {
            String value = swaggerAdapter.getApiOperationValue(method);
            if (StringUtils.isNotEmpty(value)) {
                return value;
            }
        }

        // 兼容原有的直接注解方式（向后兼容）
        if (method.isAnnotationPresent(ApiOperation.class)) {
            ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
            String value = apiOperation.value();
            if (StringUtils.isNotEmpty(value)) {
                return value;
            }
        }

        return "";
    }

    /**
     * 获取方法响应描述
     */
    public String getMethodResponseDescription(Method method) {
        if (swaggerAdapter != null) {
            String notes = swaggerAdapter.getApiOperationNotes(method);
            if (StringUtils.isNotBlank(notes)) {
                return notes;
            }
            String value = swaggerAdapter.getApiOperationValue(method);
            if (StringUtils.isNotBlank(value)) {
                return value + "的返回结果";
            }
        }

        // 兼容原有的直接注解方式（向后兼容）
        if (method.isAnnotationPresent(ApiOperation.class)) {
            ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
            String notes = apiOperation.notes();
            if (StringUtils.isNotBlank(notes)) {
                return notes;
            }
            String value = apiOperation.value();
            if (StringUtils.isNotBlank(value)) {
                return value + "的返回结果";
            }
        }
        return "返回结果";
    }

    /**
     * 解析API路径 - 兼容Spring Boot 3.x
     */
    public static List<String> resolveApiPaths(RequestMappingInfo mappingInfo) {
        List<String> paths = new ArrayList<>();
//            List<String> apiPath = new ArrayList<>();
//            if (mappingInfo.getPatternsCondition() != null) {
//                apiPath.addAll(mappingInfo.getPatternsCondition().getPatterns());
//            } else if (mappingInfo.getPathPatternsCondition() != null) {
//                mappingInfo.getPathPatternsCondition().getDirectPaths().forEach(apiPath::add);
//            }
        try {
            Method getPathPatternsConditionMethod = mappingInfo.getClass().getMethod("getPathPatternsCondition");
            Object pathPatternsCondition = getPathPatternsConditionMethod.invoke(mappingInfo);
            if (pathPatternsCondition != null) {
                Method getDirectPaths = pathPatternsCondition.getClass().getMethod("getDirectPaths");
                Collection<?> directPaths = (Collection<?>) getDirectPaths.invoke(pathPatternsCondition);
                for (Object path : directPaths) {
                    paths.add(path.toString());
                }
            } else {
                // Spring Boot 2.x fallback
                if (mappingInfo.getPatternsCondition() != null) {
                    paths.addAll(mappingInfo.getPatternsCondition().getPatterns());
                }
            }
        } catch (NoSuchMethodException e) {
            // Spring Boot 2.x
            if (mappingInfo.getPatternsCondition() != null) {
                paths.addAll(mappingInfo.getPatternsCondition().getPatterns());
            }
        } catch (Exception e) {
            throw new RuntimeException("解析 RequestMappingInfo 失败", e);
        }
        return paths;
    }
}
