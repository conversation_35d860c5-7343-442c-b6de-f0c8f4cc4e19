package com.yxt.safecenter.sdk.online.core.swagger;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;

/**
 * Swagger适配器工厂
 * 根据环境和依赖自动选择合适的Swagger适配器
 */
@Slf4j
public class SwaggerAdapterFactory {

    // Swagger 版本枚举
    public enum SwaggerVersion {
        SWAGGER2, SWAGGER3, NONE
    }

    /**
     * 创建Swagger适配器
     */
    public static SwaggerAnnotationAdapter createAdapter(Environment environment) {
        SwaggerVersion version = detectSwaggerVersion(environment);
        log.info("检测到的 Swagger 版本: {}", version);

        switch (version) {
            case SWAGGER2:
                return new Swagger2Adapter();
            case SWAGGER3:
                return new Swagger3Adapter();
            default:
                log.warn("未检测到 Swagger 依赖，将使用默认适配器");
                return new NoSwaggerAdapter();
        }
    }

    /**
     * 检测 Swagger 版本
     */
    private static SwaggerVersion detectSwaggerVersion(Environment environment) {
        // 检查配置属性
        String configuredVersion = environment.getProperty("biz.safecenter.swagger.version");
        if (StringUtils.isNotBlank(configuredVersion)) {
            if ("swagger2".equalsIgnoreCase(configuredVersion) || "2".equals(configuredVersion)) {
                return SwaggerVersion.SWAGGER2;
            } else if ("swagger3".equalsIgnoreCase(configuredVersion) || 
                      "openapi3".equalsIgnoreCase(configuredVersion) || 
                      "3".equals(configuredVersion)) {
                return SwaggerVersion.SWAGGER3;
            }
        }

        // 通过类路径检测
        try {
            // 检测 Swagger3/OpenAPI3
            Class.forName("io.swagger.v3.oas.annotations.Operation");
            return SwaggerVersion.SWAGGER3;
        } catch (ClassNotFoundException e) {
            // 继续检测 Swagger2
        }

        try {
            // 检测 Swagger2
            Class.forName("io.swagger.annotations.ApiOperation");
            return SwaggerVersion.SWAGGER2;
        } catch (ClassNotFoundException e) {
            // 都没有找到
        }

        return SwaggerVersion.NONE;
    }
}
