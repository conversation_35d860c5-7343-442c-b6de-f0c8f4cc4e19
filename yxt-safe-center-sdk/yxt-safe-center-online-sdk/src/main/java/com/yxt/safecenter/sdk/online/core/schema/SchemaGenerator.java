package com.yxt.safecenter.sdk.online.core.schema;

import com.yxt.safecenter.sdk.online.core.resolver.GenericTypeResolver;
import com.yxt.safecenter.sdk.online.core.swagger.SwaggerAnnotationAdapter;
import com.yxt.safecenter.sdk.online.core.util.TypeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Schema生成器
 * 负责生成JSON Schema结构
 */
@Slf4j
@RequiredArgsConstructor
public class SchemaGenerator {

    private final SwaggerAnnotationAdapter swaggerAdapter;
    private final GenericTypeResolver genericTypeResolver;

    /**
     * 创建类型Schema（通用方法）
     */
    public Map<String, Object> createTypeSchema(Class<?> clazz, Type genericType) {
        return createTypeSchema(clazz, genericType, new HashSet<>());
    }

    /**
     * 创建类型Schema（带循环引用检测）
     */
    public Map<String, Object> createTypeSchema(Class<?> clazz, Type genericType, Set<Class<?>> visitedClasses) {
        Map<String, Object> schema = new HashMap<>();

        try {
            // 处理泛型类型
            if (genericType instanceof ParameterizedType) {
                ParameterizedType paramType = (ParameterizedType) genericType;
                Class<?> rawType = (Class<?>) paramType.getRawType();

                String type = TypeUtils.getJsonSchemaType(rawType);
                schema.put("type", type);

                // 如果是枚举类型，添加enum属性
                if ("string".equals(type) && rawType.isEnum()) {
                    List<String> enumValues = TypeUtils.getEnumValues(rawType);
                    if (!enumValues.isEmpty()) {
                        schema.put("enums", enumValues);
                    }
                } else if ("object".equals(type)) {
                    // 添加对象描述
                    String description = getClassDescription(rawType);
                    if (StringUtils.isNotBlank(description)) {
                        schema.put("description", description);
                    }

                    // 解析对象的properties
                    Map<String, Object> properties = createPropertiesFromClass(rawType, visitedClasses);

                    // 处理泛型字段
                    Type[] typeArgs = paramType.getActualTypeArguments();
                    for (int i = 0; i < typeArgs.length; i++) {
                        genericTypeResolver.replaceGenericFieldsInProperties(properties, rawType, paramType, typeArgs[i], i);
                    }

                    if (!properties.isEmpty()) {
                        schema.put("properties", properties);
                    }
                } else if ("array".equals(type)) {
                    Map<String, Object> items = createArrayItemsSchema(rawType, genericType, visitedClasses);
                    if (!items.isEmpty()) {
                        schema.put("items", items);
                    }
                }
            } else {
                // 非泛型类型
                String type = TypeUtils.getJsonSchemaType(clazz);
                schema.put("type", type);

                // 如果是枚举类型，添加enum属性
                if ("string".equals(type) && clazz.isEnum()) {
                    List<String> enumValues = TypeUtils.getEnumValues(clazz);
                    if (!enumValues.isEmpty()) {
                        schema.put("enums", enumValues);
                    }
                } else if ("object".equals(type)) {
                    // 添加对象描述
                    String description = getClassDescription(clazz);
                    if (StringUtils.isNotBlank(description)) {
                        schema.put("description", description);
                    }

                    Map<String, Object> properties = createPropertiesFromClass(clazz, visitedClasses);
                    if (!properties.isEmpty()) {
                        schema.put("properties", properties);
                    }
                } else if ("array".equals(type)) {
                    Map<String, Object> items = createArrayItemsSchema(clazz, genericType, visitedClasses);
                    if (!items.isEmpty()) {
                        schema.put("items", items);
                    }
                }
            }
        } catch (Exception e) {
            log.error("创建类型Schema失败: {}", clazz.getName(), e);
            // 降级处理
            schema.put("type", TypeUtils.getJsonSchemaType(clazz));
        }

        return schema;
    }

    /**
     * 从类创建properties
     */
    public Map<String, Object> createPropertiesFromClass(Class<?> clazz) {
        return createPropertiesFromClass(clazz, new HashSet<>());
    }

    /**
     * 从类创建properties（带循环引用检测）
     */
    public Map<String, Object> createPropertiesFromClass(Class<?> clazz, Set<Class<?>> visitedClasses) {
        Map<String, Object> properties = new HashMap<>();

        if (TypeUtils.isBasicType(clazz) || visitedClasses.contains(clazz)) {
            return properties;
        }

        // 添加到已访问类集合中，防止循环引用
        visitedClasses.add(clazz);

        try {
            // 获取当前类及所有父类的字段
            Class<?> currentClass = clazz;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID，以及已经处理过的字段
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName()) ||
                            properties.containsKey(field.getName())) {
                        continue;
                    }

                    Map<String, Object> fieldSchema = createFieldSchema(field, visitedClasses);
                    properties.put(field.getName(), fieldSchema);
                }
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("创建properties失败: {}", clazz.getName(), e);
        } finally {
            // 从已访问类集合中移除，允许在其他分支中重新访问
            visitedClasses.remove(clazz);
        }

        return properties;
    }

    /**
     * 创建字段Schema
     */
    public Map<String, Object> createFieldSchema(Field field) {
        return createFieldSchema(field, new HashSet<>());
    }

    /**
     * 创建字段Schema（带循环引用检测）
     */
    public Map<String, Object> createFieldSchema(Field field, Set<Class<?>> visitedClasses) {
        Map<String, Object> schema = new HashMap<>();

        String type = TypeUtils.getJsonSchemaType(field.getType());
        schema.put("type", type);

        // 是否必须
        boolean required = TypeUtils.isFieldRequired(field);
        schema.put("required", required);

        // 字段说明
        String description = getFieldDescription(field);
        if (StringUtils.isNotBlank(description)) {
            schema.put("description", description);
        }

        // 如果是枚举类型，添加enum属性
        if ("string".equals(type) && field.getType().isEnum()) {
            List<String> enumValues = TypeUtils.getEnumValues(field.getType());
            if (!enumValues.isEmpty()) {
                schema.put("enums", enumValues);
            }
        }
        // 如果是对象类型，递归添加properties
        else if ("object".equals(type)) {
            // 检查是否已经访问过此类，防止循环引用
            if (!visitedClasses.contains(field.getType())) {
                // 如果字段描述为空，尝试使用类的ApiModel描述
                if (!schema.containsKey("description")) {
                    String classDescription = getClassDescription(field.getType());
                    if (StringUtils.isNotBlank(classDescription)) {
                        schema.put("description", classDescription);
                    }
                }

                Map<String, Object> properties = createPropertiesFromClass(field.getType(), visitedClasses);
                if (!properties.isEmpty()) {
                    schema.put("properties", properties);
                }
            } else {
                // 循环引用时，只保留基本信息，不展开properties
                schema.put("description", "循环引用: " + field.getType().getSimpleName());
            }
        }
        // 如果是数组类型，添加items
        else if ("array".equals(type)) {
            Map<String, Object> items = createArrayItemsSchema(field.getType(), field.getGenericType(), visitedClasses);
            if (!items.isEmpty()) {
                schema.put("items", items);
            }
        }

        return schema;
    }

    /**
     * 获取类描述（从ApiModel注解）
     */
    private String getClassDescription(Class<?> clazz) {
        if (swaggerAdapter != null) {
            String value = swaggerAdapter.getApiModelValue(clazz);
            if (StringUtils.isNotBlank(value)) {
                return value;
            }
            String description = swaggerAdapter.getApiModelDescription(clazz);
            if (StringUtils.isNotBlank(description)) {
                return description;
            }
        }
        return "";
    }

    /**
     * 获取字段描述
     */
    private String getFieldDescription(Field field) {
        if (swaggerAdapter != null) {
            String description = swaggerAdapter.getApiModelPropertyValue(field);
            if (StringUtils.isNotBlank(description)) {
                return description;
            }
        }
        return "";
    }

    /**
     * 创建数组items的Schema
     */
    public Map<String, Object> createArrayItemsSchema(Class<?> arrayClass, Type genericType, Set<Class<?>> visitedClasses) {
        Map<String, Object> items = new HashMap<>();

        try {
            // 处理泛型数组，如List<String>、List<SomeClass>等
            if (genericType instanceof ParameterizedType) {
                ParameterizedType paramType = (ParameterizedType) genericType;
                Type[] typeArgs = paramType.getActualTypeArguments();
                if (typeArgs.length > 0) {
                    Type itemType = typeArgs[0];
                    if (itemType instanceof Class) {
                        Class<?> itemClass = (Class<?>) itemType;
                        String itemSchemaType = TypeUtils.getJsonSchemaType(itemClass);
                        items.put("type", itemSchemaType);

                        // 如果是枚举类型，添加enum属性
                        if ("string".equals(itemSchemaType) && itemClass.isEnum()) {
                            List<String> enumValues = TypeUtils.getEnumValues(itemClass);
                            if (!enumValues.isEmpty()) {
                                items.put("enums", enumValues);
                            }
                        } else if ("object".equals(itemSchemaType)) {
                            // 检查是否已经访问过此类，防止循环引用
                            if (!visitedClasses.contains(itemClass)) {
                                // 添加对象描述
                                String description = getClassDescription(itemClass);
                                if (StringUtils.isNotBlank(description)) {
                                    items.put("description", description);
                                }

                                Map<String, Object> properties = createPropertiesFromClass(itemClass, visitedClasses);
                                if (!properties.isEmpty()) {
                                    items.put("properties", properties);
                                }
                            } else {
                                // 循环引用时，只保留基本信息
                                items.put("description", "循环引用: " + itemClass.getSimpleName());
                            }
                        }
                    } else if (itemType instanceof ParameterizedType) {
                        // 处理嵌套的泛型类型，如List<ResponseBase<DemoResp>>中的ResponseBase<DemoResp>
                        ParameterizedType itemParamType = (ParameterizedType) itemType;
                        Class<?> itemRawType = (Class<?>) itemParamType.getRawType();
                        String itemSchemaType = TypeUtils.getJsonSchemaType(itemRawType);
                        items.put("type", itemSchemaType);

                        if ("object".equals(itemSchemaType)) {
                            // 递归调用createTypeSchema来处理复杂的泛型类型
                            Map<String, Object> itemSchema = createTypeSchema(itemRawType, itemType, visitedClasses);
                            // 将itemSchema的内容合并到items中，但排除type字段（已经设置过了）
                            for (Map.Entry<String, Object> entry : itemSchema.entrySet()) {
                                if (!"type".equals(entry.getKey())) {
                                    items.put(entry.getKey(), entry.getValue());
                                }
                            }
                        }
                    }
                }
            }
            // 处理数组类型，如String[]、SomeClass[]等
            else if (arrayClass.isArray()) {
                Class<?> componentType = arrayClass.getComponentType();
                String itemSchemaType = TypeUtils.getJsonSchemaType(componentType);
                items.put("type", itemSchemaType);

                // 如果是枚举类型，添加enum属性
                if ("string".equals(itemSchemaType) && componentType.isEnum()) {
                    List<String> enumValues = TypeUtils.getEnumValues(componentType);
                    if (!enumValues.isEmpty()) {
                        items.put("enums", enumValues);
                    }
                } else if ("object".equals(itemSchemaType)) {
                    // 检查是否已经访问过此类，防止循环引用
                    if (!visitedClasses.contains(componentType)) {
                        // 添加对象描述
                        String description = getClassDescription(componentType);
                        if (StringUtils.isNotBlank(description)) {
                            items.put("description", description);
                        }

                        Map<String, Object> properties = createPropertiesFromClass(componentType, visitedClasses);
                        if (!properties.isEmpty()) {
                            items.put("properties", properties);
                        }
                    } else {
                        // 循环引用时，只保留基本信息
                        items.put("description", "循环引用: " + componentType.getSimpleName());
                    }
                }
            }
        } catch (Exception e) {
            log.error("创建数组items schema失败", e);
        }

        return items;
    }


}
