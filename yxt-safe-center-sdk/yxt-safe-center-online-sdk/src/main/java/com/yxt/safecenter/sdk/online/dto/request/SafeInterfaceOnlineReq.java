package com.yxt.safecenter.sdk.online.dto.request;

import com.yxt.safecenter.sdk.online.enums.ReqMethod;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 服务接口表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SafeInterfaceOnlineReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务名
     */
    private String applicationName;

    /**
     * api类
     */
    private String apiClass;

    /**
     * api方法
     */
    private String apiMethod;

    /**
     * 请求方式
     *
     * @see ReqMethod
     */
    private String apiWay;

    /**
     * path路径
     */
    private String apiPath;

    /**
     * 接口名称
     */
    private String apiName;

    /**
     * 请求参数信息
     */
    private String reqParamsInfo;

    /**
     * 响应参数信息
     */
    private String respParamsInfo;
}
