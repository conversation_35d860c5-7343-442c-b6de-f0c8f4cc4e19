package com.yxt.safecenter.sdk.online.core.swagger;

import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * Swagger3/OpenAPI3 适配器实现
 */
public class Swagger3Adapter implements SwaggerAnnotationAdapter {
    
    @Override
    public String getApiOperationValue(Method method) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Annotation> operationClass = (Class<? extends Annotation>) 
                Class.forName("io.swagger.v3.oas.annotations.Operation");
            Annotation annotation = method.getAnnotation(operationClass);
            if (annotation != null) {
                Method summaryMethod = operationClass.getMethod("summary");
                String summary = (String) summaryMethod.invoke(annotation);
                if (StringUtils.isNotBlank(summary)) {
                    return summary;
                }
                // 如果 summary 为空，尝试获取 description
                Method descriptionMethod = operationClass.getMethod("description");
                return (String) descriptionMethod.invoke(annotation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }

    @Override
    public String getApiOperationNotes(Method method) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Annotation> operationClass = (Class<? extends Annotation>) 
                Class.forName("io.swagger.v3.oas.annotations.Operation");
            Annotation annotation = method.getAnnotation(operationClass);
            if (annotation != null) {
                Method descriptionMethod = operationClass.getMethod("description");
                return (String) descriptionMethod.invoke(annotation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }

    @Override
    public String getApiParamValue(Parameter parameter) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Annotation> parameterClass = (Class<? extends Annotation>) 
                Class.forName("io.swagger.v3.oas.annotations.Parameter");
            Annotation annotation = parameter.getAnnotation(parameterClass);
            if (annotation != null) {
                Method descriptionMethod = parameterClass.getMethod("description");
                return (String) descriptionMethod.invoke(annotation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }

    @Override
    public String getApiModelValue(Class<?> clazz) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Annotation> schemaClass = (Class<? extends Annotation>) 
                Class.forName("io.swagger.v3.oas.annotations.media.Schema");
            Annotation annotation = clazz.getAnnotation(schemaClass);
            if (annotation != null) {
                Method nameMethod = schemaClass.getMethod("name");
                String name = (String) nameMethod.invoke(annotation);
                if (StringUtils.isNotBlank(name)) {
                    return name;
                }
                // 如果 name 为空，尝试获取 title
                Method titleMethod = schemaClass.getMethod("title");
                return (String) titleMethod.invoke(annotation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }

    @Override
    public String getApiModelDescription(Class<?> clazz) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Annotation> schemaClass = (Class<? extends Annotation>) 
                Class.forName("io.swagger.v3.oas.annotations.media.Schema");
            Annotation annotation = clazz.getAnnotation(schemaClass);
            if (annotation != null) {
                Method descriptionMethod = schemaClass.getMethod("description");
                return (String) descriptionMethod.invoke(annotation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }

    @Override
    public String getApiModelPropertyValue(Field field) {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Annotation> schemaClass = (Class<? extends Annotation>) 
                Class.forName("io.swagger.v3.oas.annotations.media.Schema");
            Annotation annotation = field.getAnnotation(schemaClass);
            if (annotation != null) {
                Method descriptionMethod = schemaClass.getMethod("description");
                return (String) descriptionMethod.invoke(annotation);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "";
    }
}
