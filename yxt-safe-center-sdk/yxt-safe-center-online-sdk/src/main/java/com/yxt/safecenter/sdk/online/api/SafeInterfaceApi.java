package com.yxt.safecenter.sdk.online.api;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.common.model.constant.CommonConstant;
import com.yxt.safecenter.sdk.online.dto.request.SafeInterfaceOnlineReq;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * <AUTHOR>
 */
@FeignClient(name = CommonConstant.SYSTEM_NAME)
public interface SafeInterfaceApi {

    @ApiOperation(value = "接口上报", notes = "接口上报")
    @PostMapping(value = "/api/interface/online")
    ResponseBase<Void> online(@RequestBody List<SafeInterfaceOnlineReq> onlineReqList);
}
