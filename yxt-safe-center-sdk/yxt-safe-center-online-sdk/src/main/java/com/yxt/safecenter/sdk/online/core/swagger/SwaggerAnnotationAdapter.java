package com.yxt.safecenter.sdk.online.core.swagger;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * Swagger 注解适配器接口
 * 用于适配不同版本的Swagger注解
 */
public interface SwaggerAnnotationAdapter {
    
    /**
     * 获取API操作的值/摘要
     */
    String getApiOperationValue(Method method);

    /**
     * 获取API操作的备注/描述
     */
    String getApiOperationNotes(Method method);

    /**
     * 获取API参数的描述
     */
    String getApiParamValue(Parameter parameter);

    /**
     * 获取API模型的值/名称
     */
    String getApiModelValue(Class<?> clazz);

    /**
     * 获取API模型的描述
     */
    String getApiModelDescription(Class<?> clazz);

    /**
     * 获取API模型属性的描述
     */
    String getApiModelPropertyValue(Field field);
}
