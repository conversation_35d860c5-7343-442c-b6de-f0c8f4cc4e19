package com.yxt.safecenter.sdk.online.core;

import com.google.common.collect.Sets;
import com.yxt.lang.util.JsonUtils;
import com.yxt.safecenter.sdk.online.api.SafeInterfaceApi;
import com.yxt.safecenter.sdk.online.core.analyzer.ParameterAnalyzer;
import com.yxt.safecenter.sdk.online.core.analyzer.ResponseAnalyzer;
import com.yxt.safecenter.sdk.online.core.extractor.ApiInfoExtractor;
import com.yxt.safecenter.sdk.online.core.resolver.GenericTypeResolver;
import com.yxt.safecenter.sdk.online.core.schema.SchemaGenerator;
import com.yxt.safecenter.sdk.online.core.swagger.SwaggerAdapterFactory;
import com.yxt.safecenter.sdk.online.core.swagger.SwaggerAnnotationAdapter;
import com.yxt.safecenter.sdk.online.dto.request.SafeInterfaceOnlineReq;
import com.yxt.safecenter.sdk.online.enums.ReqMethod;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import java.lang.reflect.*;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class ControllerScanner implements CommandLineRunner {

    private final RequestMappingHandlerMapping requestMappingHandlerMapping;
    private final SafeInterfaceApi safeInterfaceApi;
    private final Environment environment;
    private final ApplicationContext applicationContext;
    private Set<String> projectPackagePres = Sets.newHashSet("cn.hydee", "com.hydee");

    // 组件依赖
    private SwaggerAnnotationAdapter swaggerAdapter;
    private ApiInfoExtractor apiInfoExtractor;
    private ParameterAnalyzer parameterAnalyzer;
    private ResponseAnalyzer responseAnalyzer;

    @Override
    public void run(String... args) {
        String[] activeProfiles = environment.getActiveProfiles();
        String applicationName = environment.getProperty("spring.application.name");
        String property = environment.getProperty("biz.safecenter.online.open");
        String activeProfile = activeProfiles.length == 0 ? "default" : StringUtils.join(activeProfiles, ",");
        log.info("-------------applicationName: " + applicationName);
        log.info("-------------activeProfile: " + activeProfile);
        log.info("--------------------------");
        if ("local".equals(activeProfile) || (property != null && property.equals(Boolean.FALSE.toString()))) {
            return;
        }

        // 初始化组件
        initComponents();

        // 获取所有的请求映射信息
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();

        List<SafeInterfaceOnlineReq> serverReqList = new ArrayList<>();
        Set<String> needSkipControllerSet = new HashSet<>();
        // 遍历所有的请求映射
        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethods.entrySet()) {
            RequestMappingInfo mappingInfo = entry.getKey();
            HandlerMethod handlerMethod = entry.getValue();

            // 获取控制器类的包名
            String packageName = handlerMethod.getBeanType().getPackage().getName();

            // 过滤接口
            if (invalidPackage(handlerMethod, packageName)) {
                needSkipControllerSet.add(handlerMethod.getBeanType().getSimpleName());
                continue;
            }

            // 获取请求路径 - 兼容Spring Boot 3.x
            List<String> apiPath = ApiInfoExtractor.resolveApiPaths(mappingInfo);

            // 获取HTTP方法类型
            List<String> reqMethods = new ArrayList<>();
            if (CollectionUtils.isEmpty(mappingInfo.getMethodsCondition().getMethods())) {
                reqMethods = Arrays.stream(ReqMethod.values()).map(ReqMethod::name).collect(Collectors.toList());
            } else {
                for (RequestMethod httpMethod : mappingInfo.getMethodsCondition().getMethods()) {
                    reqMethods.add(httpMethod.name());
                }
            }

            // 获取控制器方法的类名和方法名
            String className = handlerMethod.getBeanType().getSimpleName();
            String methodName = handlerMethod.getMethod().getName();
            Method method = handlerMethod.getMethod();

            // 解析请求参数信息
            String reqInfo = parameterAnalyzer.parseRequestInfo(method);
            // 解析响应参数信息
            String respInfo = responseAnalyzer.parseResponseInfo(method);
            // 解析接口名称信息
            String apiName = apiInfoExtractor.parseApiName(method);

            SafeInterfaceOnlineReq safeInterfaceOnlineReq = new SafeInterfaceOnlineReq();
            safeInterfaceOnlineReq.setApplicationName(applicationName);
            safeInterfaceOnlineReq.setApiClass(className);
            safeInterfaceOnlineReq.setApiMethod(methodName);
//            safeInterfaceOnlineReq.setNeibu(isFeignClient(handlerMethod)); // 是否是内部接口 todo:gx
            safeInterfaceOnlineReq.setApiWay(JsonUtils.toJson(reqMethods));
            safeInterfaceOnlineReq.setApiPath(JsonUtils.toJson(apiPath));
            safeInterfaceOnlineReq.setReqParamsInfo(reqInfo);
            safeInterfaceOnlineReq.setRespParamsInfo(respInfo);
            safeInterfaceOnlineReq.setApiName(apiName);
            serverReqList.add(safeInterfaceOnlineReq);
        }
        log.info("需要过滤的非本项目接口类：{}", JsonUtils.toJson(needSkipControllerSet));

        // 如果检测到条件不符合要求，则提前终止
        try {
            log.info("------------------------------------数据list,{}", JsonUtils.toJson(serverReqList));
            safeInterfaceApi.online(serverReqList);
        } catch (Exception e) {
            log.error("------------------------------------接口上报异常:终止应用！", e);
            System.exit(1);
        }
    }

    /**
     * 初始化所有组件
     */
    private void initComponents() {
        // 初始化项目包名前缀
        initProjectPackagePres();

        // 初始化 Swagger 适配器
        swaggerAdapter = SwaggerAdapterFactory.createAdapter(environment);

        // 初始化各个组件
        apiInfoExtractor = new ApiInfoExtractor(swaggerAdapter);

        SchemaGenerator schemaGenerator = new SchemaGenerator(swaggerAdapter, null); // GenericTypeResolver会在后面设置
        GenericTypeResolver genericTypeResolver = new GenericTypeResolver(schemaGenerator);

        // 更新SchemaGenerator的GenericTypeResolver引用
        schemaGenerator = new SchemaGenerator(swaggerAdapter, genericTypeResolver);

        parameterAnalyzer = new ParameterAnalyzer(swaggerAdapter, schemaGenerator);
        responseAnalyzer = new ResponseAnalyzer(schemaGenerator, genericTypeResolver);
    }

    /**
     * 初始化当前项目的包名前缀（基于主应用类的包）
     */
    private void initProjectPackagePres() {
        try {
            // 查找带有@SpringBootApplication注解的主类（项目入口类）
            Map<String, Object> bootBeans = applicationContext.getBeansWithAnnotation(SpringBootApplication.class);
            if (!bootBeans.isEmpty()) {
                // 取第一个主类（通常一个项目只有一个）
                Class<?> mainClass = bootBeans.values().iterator().next().getClass();
                // 获取主类的包名（即项目根包）
                String mainPackage = mainClass.getPackage().getName();
                projectPackagePres.add(mainPackage);
            } else {
                // 未找到主类时，fallback到原有默认前缀
                log.warn("未找到主应用类，使用默认包名前缀");
            }
        } catch (Exception e) {
            // 异常时fallback到默认前缀
            log.error("获取项目包名前缀失败，使用默认值", e);
        }
    }

    private boolean invalidPackage(HandlerMethod handlerMethod, String packageName) {
        boolean skip = true;
        for (String packagePre : projectPackagePres) {
            if (packageName.startsWith(packagePre)) {
                skip = false;
                break; // 如果不在指定的包内，跳过当前控制器
            }
        }
        return skip;
    }
}
