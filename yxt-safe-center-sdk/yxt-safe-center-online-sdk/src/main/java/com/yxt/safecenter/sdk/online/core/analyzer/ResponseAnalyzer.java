package com.yxt.safecenter.sdk.online.core.analyzer;

import com.yxt.lang.util.JsonUtils;
import com.yxt.safecenter.sdk.online.core.resolver.GenericTypeResolver;
import com.yxt.safecenter.sdk.online.core.schema.SchemaGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

/**
 * 响应分析器
 * 负责解析Controller方法的响应信息
 */
@Slf4j
@RequiredArgsConstructor
public class ResponseAnalyzer {

    private final SchemaGenerator schemaGenerator;
    private final GenericTypeResolver genericTypeResolver;

    /**
     * 解析响应信息
     */
    public String parseResponseInfo(Method method) {
        try {
            Map<String, Object> responseInfo = new HashMap<>();

            // 获取返回类型
            Type returnType = method.getGenericReturnType();
            Class<?> returnClass = method.getReturnType();

            // 创建响应schema
            Map<String, Object> respSchema = schemaGenerator.createTypeSchema(returnClass, returnType);
            responseInfo.put("resp", respSchema);

            return JsonUtils.toJson(responseInfo);
        } catch (Exception e) {
            log.error("解析响应信息失败: {}", method.getName(), e);
            return "{}";
        }
    }

    /**
     * 解析参数化返回类型
     */
    public Map<String, Object> parseParameterizedReturnType(ParameterizedType parameterizedType) {
        Map<String, Object> responseInfo = new HashMap<>();

        // 解析外层类型（如ResponseBase）的字段
        Class<?> rawType = (Class<?>) parameterizedType.getRawType();
        responseInfo.putAll(parseClassFieldsWithInheritance(rawType));

        // 解析所有泛型参数
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        for (int i = 0; i < actualTypeArguments.length; i++) {
            Type actualType = actualTypeArguments[i];
            Map<String, Object> genericFieldInfo = genericTypeResolver.parseGenericType(actualType);

            if (!genericFieldInfo.isEmpty()) {
                // 通过反射检查外层类的字段，找到对应位置的泛型字段并替换
                genericTypeResolver.replaceGenericFieldsInResponse(responseInfo, rawType, parameterizedType, genericFieldInfo, i);
            }
        }

        return responseInfo;
    }

    /**
     * 解析类的字段信息（包括父类字段）
     */
    private Map<String, Object> parseClassFieldsWithInheritance(Class<?> clazz) {
        Map<String, Object> fieldsInfo = new HashMap<>();

        if (isBasicType(clazz)) {
            return fieldsInfo;
        }

        try {
            // 获取当前类及所有父类的字段
            Class<?> currentClass = clazz;
            while (currentClass != null && currentClass != Object.class) {
                java.lang.reflect.Field[] fields = currentClass.getDeclaredFields();
                for (java.lang.reflect.Field field : fields) {
                    // 跳过静态字段和serialVersionUID，以及已经处理过的字段
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName()) ||
                            fieldsInfo.containsKey(field.getName())) {
                        continue;
                    }

                    Map<String, Object> fieldInfo = new HashMap<>();
                    fieldInfo.put("name", field.getName());
                    fieldInfo.put("type", getSimpleTypeName(field.getType()));
                    fieldInfo.put("required", isFieldRequired(field));
                    fieldInfo.put("description", getFieldDescription(field));
                    fieldsInfo.put(field.getName(), fieldInfo);
                }

                // 移动到父类
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("解析类字段失败: {}", clazz.getName(), e);
        }

        return fieldsInfo;
    }

    // 辅助方法委托给TypeUtils
    private boolean isBasicType(Class<?> clazz) {
        return com.yxt.safecenter.sdk.online.core.util.TypeUtils.isBasicType(clazz);
    }

    private String getSimpleTypeName(Class<?> clazz) {
        return com.yxt.safecenter.sdk.online.core.util.TypeUtils.getSimpleTypeName(clazz);
    }

    private boolean isFieldRequired(java.lang.reflect.Field field) {
        return com.yxt.safecenter.sdk.online.core.util.TypeUtils.isFieldRequired(field);
    }

    private String getFieldDescription(java.lang.reflect.Field field) {
        // 这里需要访问SwaggerAdapter，但为了避免循环依赖，暂时返回空字符串
        // 实际使用时可以通过构造函数注入SwaggerAdapter
        return "";
    }
}
