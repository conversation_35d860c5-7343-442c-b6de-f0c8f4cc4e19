package com.yxt.safecenter.sdk.online.core.resolver;

import com.yxt.safecenter.sdk.online.core.schema.SchemaGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 泛型类型解析器
 * 负责处理复杂的泛型类型解析和替换
 */
@Slf4j
@RequiredArgsConstructor
public class GenericTypeResolver {

    private final SchemaGenerator schemaGenerator;

    /**
     * 在properties中替换泛型字段
     */
    public void replaceGenericFieldsInProperties(Map<String, Object> properties, Class<?> rawType,
                                                 ParameterizedType paramType, Type actualType, int typeArgIndex) {
        try {
            // 遍历当前类及父类的所有字段
            Class<?> currentClass = rawType;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName())) {
                        continue;
                    }

                    String fieldName = field.getName();
                    if (!properties.containsKey(fieldName)) {
                        continue;
                    }

                    // 检查字段是否是泛型类型
                    Type fieldGenericType = field.getGenericType();
                    if (fieldGenericType instanceof TypeVariable) {
                        TypeVariable<?> typeVar = (TypeVariable<?>) fieldGenericType;
                        if (isTypeVariableMatch(typeVar, rawType, actualType, typeArgIndex)) {
                            // 替换泛型字段的schema
                            Map<String, Object> fieldSchema = schemaGenerator.createTypeSchema(
                                    actualType instanceof Class ? (Class<?>) actualType : Object.class,
                                    actualType
                            );
                            // 保留原有的required和description信息
                            Map<String, Object> originalSchema = (Map<String, Object>) properties.get(fieldName);
                            if (originalSchema.containsKey("required")) {
                                fieldSchema.put("required", originalSchema.get("required"));
                            }
                            if (originalSchema.containsKey("description")) {
                                fieldSchema.put("description", originalSchema.get("description"));
                            }
                            properties.put(fieldName, fieldSchema);
                        }
                    } else if (fieldGenericType instanceof ParameterizedType) {
                        ParameterizedType fieldParamType = (ParameterizedType) fieldGenericType;
                        Type[] fieldTypeArgs = fieldParamType.getActualTypeArguments();

                        // 检查字段的泛型参数是否与目标类型匹配
                        for (Type fieldTypeArg : fieldTypeArgs) {
                            if (isGenericTypeMatch(fieldTypeArg, actualType)) {
                                // 替换泛型字段的schema
                                Map<String, Object> fieldSchema = schemaGenerator.createTypeSchema(
                                        (Class<?>) fieldParamType.getRawType(),
                                        fieldGenericType
                                );
                                // 保留原有的required和description信息
                                Map<String, Object> originalSchema = (Map<String, Object>) properties.get(fieldName);
                                if (originalSchema.containsKey("required")) {
                                    fieldSchema.put("required", originalSchema.get("required"));
                                }
                                if (originalSchema.containsKey("description")) {
                                    fieldSchema.put("description", originalSchema.get("description"));
                                }
                                properties.put(fieldName, fieldSchema);
                                break;
                            }
                        }
                    }
                }
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("替换properties中的泛型字段失败", e);
        }
    }

    /**
     * 通过反射检查并替换响应中的泛型字段
     */
    public void replaceGenericFieldsInResponse(Map<String, Object> responseInfo, Class<?> rawType,
                                               ParameterizedType parameterizedType, Map<String, Object> genericFieldInfo, int typeArgIndex) {
        try {
            // 获取泛型参数类型
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length <= typeArgIndex || genericFieldInfo.isEmpty()) {
                return;
            }

            Type targetType = actualTypeArguments[typeArgIndex];

            // 遍历当前类及父类的所有字段
            Class<?> currentClass = rawType;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName())) {
                        continue;
                    }

                    // 检查字段是否是泛型类型
                    Type fieldGenericType = field.getGenericType();
                    if (fieldGenericType instanceof ParameterizedType) {
                        ParameterizedType fieldParamType = (ParameterizedType) fieldGenericType;
                        Type[] fieldTypeArgs = fieldParamType.getActualTypeArguments();

                        // 检查字段的泛型参数是否与目标类型匹配
                        for (Type fieldTypeArg : fieldTypeArgs) {
                            if (isGenericTypeMatch(fieldTypeArg, targetType)) {
                                String fieldName = field.getName();
                                if (responseInfo.containsKey(fieldName)) {
                                    responseInfo.put(fieldName, genericFieldInfo);
                                    return; // 找到匹配的字段后返回
                                }
                            }
                        }
                    } else if (fieldGenericType instanceof TypeVariable) {
                        // 处理类型变量（如 T、E 等）
                        TypeVariable<?> typeVar = (TypeVariable<?>) fieldGenericType;
                        if (isTypeVariableMatch(typeVar, rawType, targetType, typeArgIndex)) {
                            String fieldName = field.getName();
                            if (responseInfo.containsKey(fieldName)) {
                                responseInfo.put(fieldName, genericFieldInfo);
                                return;
                            }
                        }
                    }
                }
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("替换泛型字段失败", e);
        }
    }

    /**
     * 解析泛型类型
     */
    public Map<String, Object> parseGenericType(Type type) {
        Map<String, Object> typeInfo = new HashMap<>();

        try {
            if (type instanceof Class) {
                Class<?> clazz = (Class<?>) type;
                if (!isBasicType(clazz) && !clazz.equals(Void.class)) {
                    typeInfo = parseClassFieldsWithInheritance(clazz);
                } else if (!clazz.equals(Void.class)) {
                    typeInfo = createBasicTypeInfo(clazz, "返回数据");
                }
            } else if (type instanceof ParameterizedType) {
                // 处理嵌套泛型，如List<SomeClass>、PageDTO<SomeClass>等
                ParameterizedType nestedType = (ParameterizedType) type;
                Type[] nestedArgs = nestedType.getActualTypeArguments();

                // 解析外层泛型类型（如List、PageDTO等）
                Class<?> outerClass = (Class<?>) nestedType.getRawType();
                typeInfo = parseClassFieldsWithInheritance(outerClass);

                // 处理所有嵌套的泛型参数
                for (int i = 0; i < nestedArgs.length; i++) {
                    Map<String, Object> nestedTypeInfo = parseGenericType(nestedArgs[i]);
                    if (!nestedTypeInfo.isEmpty()) {
                        // 通过反射检查外层类的字段，找到对应的泛型字段并替换
                        replaceGenericFieldsInType(typeInfo, outerClass, nestedType, nestedTypeInfo, i);
                    }
                }
            } else if (type instanceof TypeVariable) {
                // 处理类型变量，创建占位符信息
                TypeVariable<?> typeVar = (TypeVariable<?>) type;
                typeInfo.put("typeVariable", typeVar.getName());
                typeInfo.put("description", "泛型类型: " + typeVar.getName());
            }
        } catch (Exception e) {
            log.error("解析泛型类型失败: {}", type, e);
        }

        return typeInfo;
    }

    /**
     * 检查泛型类型是否匹配
     */
    private boolean isGenericTypeMatch(Type fieldType, Type methodType) {
        if (fieldType instanceof TypeVariable && methodType instanceof TypeVariable) {
            return ((TypeVariable<?>) fieldType).getName().equals(((TypeVariable<?>) methodType).getName());
        }
        if (fieldType instanceof Class && methodType instanceof Class) {
            return fieldType.equals(methodType);
        }
        if (fieldType instanceof TypeVariable || methodType instanceof TypeVariable) {
            return true; // 类型变量可以匹配任何类型
        }
        return fieldType.equals(methodType);
    }

    /**
     * 检查类型变量是否匹配（支持类型参数索引）
     */
    private boolean isTypeVariableMatch(TypeVariable<?> typeVar, Class<?> declaringClass, Type actualType, int typeArgIndex) {
        // 获取类的泛型参数声明
        TypeVariable<?>[] classTypeParams = declaringClass.getTypeParameters();
        for (int i = 0; i < classTypeParams.length; i++) {
            if (classTypeParams[i].getName().equals(typeVar.getName())) {
                // 检查类型变量的位置是否与期望的索引匹配
                return i == typeArgIndex;
            }
        }
        return false;
    }

    /**
     * 通过反射检查并替换类型中的泛型字段（用于嵌套泛型处理）
     */
    private void replaceGenericFieldsInType(Map<String, Object> typeInfo, Class<?> outerClass,
                                            ParameterizedType parameterizedType, Map<String, Object> genericFieldInfo, int typeArgIndex) {
        try {
            // 获取泛型参数类型
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length <= typeArgIndex || genericFieldInfo.isEmpty()) {
                return;
            }

            Type targetType = actualTypeArguments[typeArgIndex];

            // 遍历当前类及父类的所有字段
            Class<?> currentClass = outerClass;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName())) {
                        continue;
                    }

                    // 检查字段是否是泛型类型
                    Type fieldGenericType = field.getGenericType();
                    if (fieldGenericType instanceof ParameterizedType) {
                        ParameterizedType fieldParamType = (ParameterizedType) fieldGenericType;
                        Type[] fieldTypeArgs = fieldParamType.getActualTypeArguments();

                        // 检查字段的泛型参数是否与目标类型匹配
                        for (Type fieldTypeArg : fieldTypeArgs) {
                            if (isGenericTypeMatch(fieldTypeArg, targetType)) {
                                String fieldName = field.getName();
                                if (typeInfo.containsKey(fieldName)) {
                                    typeInfo.put(fieldName, genericFieldInfo);
                                    return;
                                }
                            }
                        }
                    } else if (fieldGenericType instanceof TypeVariable) {
                        // 处理类型变量
                        TypeVariable<?> typeVar = (TypeVariable<?>) fieldGenericType;
                        if (isTypeVariableMatch(typeVar, outerClass, targetType, typeArgIndex)) {
                            String fieldName = field.getName();
                            if (typeInfo.containsKey(fieldName)) {
                                typeInfo.put(fieldName, genericFieldInfo);
                                return;
                            }
                        }
                    }
                }
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("替换嵌套泛型字段失败", e);
        }
    }

    /**
     * 解析类的字段信息（包括父类字段）
     */
    private Map<String, Object> parseClassFieldsWithInheritance(Class<?> clazz) {
        Map<String, Object> fieldsInfo = new HashMap<>();

        if (isBasicType(clazz)) {
            return fieldsInfo;
        }

        try {
            // 获取当前类及所有父类的字段
            Class<?> currentClass = clazz;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID，以及已经处理过的字段
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName()) ||
                            fieldsInfo.containsKey(field.getName())) {
                        continue;
                    }

                    Map<String, Object> fieldInfo = new HashMap<>();
                    fieldInfo.put("name", field.getName());
                    fieldInfo.put("type", getSimpleTypeName(field.getType()));
                    fieldInfo.put("required", isFieldRequired(field));
                    fieldInfo.put("description", getFieldDescription(field));
                    fieldsInfo.put(field.getName(), fieldInfo);
                }

                // 移动到父类
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("解析类字段失败: {}", clazz.getName(), e);
        }

        return fieldsInfo;
    }

    /**
     * 创建基本类型信息
     */
    private Map<String, Object> createBasicTypeInfo(Class<?> clazz, String description) {
        Map<String, Object> info = new HashMap<>();
        info.put("type", getSimpleTypeName(clazz));
        info.put("description", description);
        info.put("required", false);
        return info;
    }

    // 辅助方法委托给TypeUtils
    private boolean isBasicType(Class<?> clazz) {
        return com.yxt.safecenter.sdk.online.core.util.TypeUtils.isBasicType(clazz);
    }

    private String getSimpleTypeName(Class<?> clazz) {
        return com.yxt.safecenter.sdk.online.core.util.TypeUtils.getSimpleTypeName(clazz);
    }

    private boolean isFieldRequired(Field field) {
        return com.yxt.safecenter.sdk.online.core.util.TypeUtils.isFieldRequired(field);
    }

    private String getFieldDescription(Field field) {
        // 这里需要访问SwaggerAdapter，但为了避免循环依赖，暂时返回空字符串
        // 实际使用时可以通过构造函数注入SwaggerAdapter
        return "";
    }
}
