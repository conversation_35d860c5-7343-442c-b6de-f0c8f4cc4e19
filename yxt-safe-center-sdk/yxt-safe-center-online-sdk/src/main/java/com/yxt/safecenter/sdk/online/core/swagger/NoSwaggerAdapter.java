package com.yxt.safecenter.sdk.online.core.swagger;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 无 Swagger 适配器实现（默认实现）
 * 当没有检测到Swagger依赖时使用
 */
public class NoSwaggerAdapter implements SwaggerAnnotationAdapter {
    
    @Override
    public String getApiOperationValue(Method method) {
        return "";
    }

    @Override
    public String getApiOperationNotes(Method method) {
        return "";
    }

    @Override
    public String getApiParamValue(Parameter parameter) {
        return "";
    }

    @Override
    public String getApiModelValue(Class<?> clazz) {
        return "";
    }

    @Override
    public String getApiModelDescription(Class<?> clazz) {
        return "";
    }

    @Override
    public String getApiModelPropertyValue(Field field) {
        return "";
    }
}
