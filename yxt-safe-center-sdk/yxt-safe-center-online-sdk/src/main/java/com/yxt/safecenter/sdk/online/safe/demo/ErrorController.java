package com.yxt.safecenter.sdk.online.safe.demo;

import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping({"/api/orders", "/api/v2/orders"})
public class ErrorController {


    @RequestMapping("/{xxxx}")
    public String xxxx(@PathVariable String orderId) {
        return "Order " + orderId;
    }


    @GetMapping({"/{orderId}", "/default2/", "/default4/"})
    public String getOrder(@PathVariable String orderId) {
        return "Order " + orderId;
    }


    @GetMapping("/zcj/{orderId}")
    public String getOrderzcj(@PathVariable String orderId) {
        return "Order " + orderId;
    }


    @GetMapping({"/orderId", "default"})
    public String createOrder() {
        return "Created order: ";
    }


    @PutMapping
    public String putMapping(@RequestBody String order) {
        return "Created order: " + order;
    }


    @DeleteMapping
    public String deleteMapping(@RequestBody String order) {
        return "Created order: " + order;
    }
}
