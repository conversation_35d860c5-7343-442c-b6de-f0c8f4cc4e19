package com.yxt.safecenter.auth.sdk.core;

import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.auth.sdk.service.SafeCenterDataService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * Since: 2025/2/25 10:53
 * Author: qs
 */
@Component
public class DataLoader implements ApplicationRunner {

    @Resource
    private SafeCenterDataService safeCenterDataService;

    @Override
    public void run(ApplicationArguments args) {
        ExLogger.logger("DataLoader").info("加载接口、应用数据开始");
        safeCenterDataService.refreshData(true);
    }

}
