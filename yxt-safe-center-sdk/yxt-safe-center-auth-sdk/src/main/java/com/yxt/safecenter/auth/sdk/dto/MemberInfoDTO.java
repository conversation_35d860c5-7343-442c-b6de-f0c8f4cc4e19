package com.yxt.safecenter.auth.sdk.dto;

import com.yxt.safecenter.auth.sdk.enums.PlatformEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/09 14:37
 */
@Data
public class MemberInfoDTO {

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会员姓名
     */
    private String memberName;

    /**
     * 会员性别
     */
    private Integer memberSex;

    /**
     * 会员手机号
     */
    private String memberPhone;

    /**
     * 会员身份证
     */
    private String memberIdcard;

    /**
     * 会员生日
     */
    private String memberBirthday;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String headUrl;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 会员地址
     */
    private String memberAddress;


    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 登录平台
     */
    private PlatformEnum platformEnum;

    private String token;

    private String memberCard;

    private Map<String, PlatformInfo> platformMap;
    /**
     * 正和祥定制化需求增加单商户支持授权多小程序,从而将会员登录平台关系改为一对多
     */
    private Map<String, List<NewPlatformInfo>> newPlatformMap;

    /**
     * UNIONID
     */
    private String unionid;

    /**
     * OPENID
     */
    private String openid;

    private Integer regChannel;
    /**
     * 是否为员工
     */
    private Boolean employeeFlag;
    @ApiModelProperty(value = "员工附属部门编码")
    private String subOrgCode;

    private String userIdString;

    @ApiModelProperty(value = "会员对应的员工编码")
    private String empCode;

    @ApiModelProperty(value = "是否是分销员（true：是，false：否）")
    private Boolean isDistributor;

    @ApiModelProperty(value = "员工姓名")
    private String empName;

    @ApiModelProperty(value = "员工角色")
    private String empRoleStr;

    /**
     * 会员状态 1-已激活 2-已注销 3-已冻结
     */
    @ApiModelProperty(value = "会员状态")
    private Integer memberStatus;

    public void clearEmpInfo() {
        this.employeeFlag = false;
        this.subOrgCode = null;
        this.empCode = null;
    }

    public String getOpenIdBy(PlatformEnum platformEnum) {
        return getOpenIdBy(platformEnum.name());
    }

    public String getOpenIdBy(String platform) {
        if (CollectionUtils.isEmpty(platformMap)) {
            return null;
        }
        // if platform is H5, openId/unionId is empty string
        return platformMap.getOrDefault(platform, new PlatformInfo()).getOpenId();
    }

    public String getOpenIdBy(String platform, String appId) {
        if (CollectionUtils.isEmpty(platformMap)) {
            NewPlatformInfo platformInfo =  getPlatformInfoByNewPlatformMap(platform, appId);
            return platformInfo == null ? null : platformInfo.getOpenId();
        }
        // if platform is H5, openId/unionId is empty string
        return platformMap.getOrDefault(platform, new PlatformInfo()).getOpenId();
    }

    private NewPlatformInfo getPlatformInfoByNewPlatformMap(String platform, String appId) {
        if (CollectionUtils.isEmpty(newPlatformMap)) {
            return null;
        }

        List<NewPlatformInfo> platformInfos = newPlatformMap.get(platform);
        if (CollectionUtils.isEmpty(platformInfos)) {
            return null;
        }

        if (StringUtils.isBlank(appId)) {
            return platformInfos.get(0);
        } else {
            for (NewPlatformInfo platformInfo : platformInfos) {
                if (StringUtils.equals(platformInfo.getAppid(), appId)) {
                    return platformInfo;
                }
            }
        }

        return null;
    }

    public String getUnionIdBy(PlatformEnum platformEnum, String appId) {
        return getUnionIdBy(platformEnum.name(), appId);
    }

    public String getUnionIdBy(String platform, String appId) {
        if (CollectionUtils.isEmpty(platformMap)) {
            NewPlatformInfo platformInfo = getPlatformInfoByNewPlatformMap(platform, appId);
            return platformInfo == null ? null : platformInfo.getUnionId();
        }
        // if platform is H5, openId/unionId is empty string
        return platformMap.getOrDefault(platform, new PlatformInfo()).getUnionId();
    }


    public String getEmpFlag() {
        if (this.employeeFlag == null) {
            return null;
        }
        return employeeFlag ? "1" : "0";
    }

    @Data
    public static class PlatformInfo {
        private String openId;
        private String unionId;
    }

    @Data
    public static class NewPlatformInfo {
        private String openId;
        private String unionId;
        private String appid;
        private String merTypeName;
    }
}
