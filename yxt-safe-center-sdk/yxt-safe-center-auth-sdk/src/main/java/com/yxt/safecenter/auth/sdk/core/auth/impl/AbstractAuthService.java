package com.yxt.safecenter.auth.sdk.core.auth.impl;

import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.auth.sdk.constants.AuthConstants;
import com.yxt.safecenter.auth.sdk.constants.RestCodeConstants;
import com.yxt.safecenter.auth.sdk.dto.InterfaceInfoDTO;
import com.yxt.safecenter.auth.sdk.enums.AuthFailStatusEnum;
import com.yxt.safecenter.auth.sdk.service.SafeCenterDataService;
import com.yxt.safecenter.auth.sdk.core.auth.AuthService;
import com.yxt.safecenter.auth.sdk.utils.HttpUtils;
import com.yxt.safecenter.auth.sdk.utils.InterfaceMatcherUtil;
import com.yxt.safecenter.auth.sdk.utils.ResultVO;
import com.yxt.safecenter.auth.sdk.utils.StringUtil;
import com.yxt.safecenter.common.model.dto.AuthInfoDTO;
import com.yxt.safecenter.common.model.enums.AuthGatewayEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.Assert;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Since: 2025/1/17 14:56
 * Author: qs
 */

@Service
public abstract class AbstractAuthService implements AuthService {

    @Value("${safe-center.gateway-channel: }")
    private String gatewayChannel;

    @Resource
    private SafeCenterDataService safeCenterDataService;

    @Override
    public Mono<Void> execute(ServerWebExchange exchange, GatewayFilterChain chain, InterfaceInfoDTO interfaceInfoDTO, String appKey) {
        // 拦截应用未授权接口
        Mono<Void> interceptAppNotAuthReqRet = interceptAppNotAuthReq(exchange, appKey);
        if (interceptAppNotAuthReqRet != null) {
            return interceptAppNotAuthReqRet;
        }
        // 接口鉴权
        return auth(exchange, chain);
    }

    // 拦截不属于本网关的请求
    // 前面有拦截，这里不再拦截
    private Mono<Void> interceptIllegalReq(ServerWebExchange exchange, InterfaceInfoDTO interfaceInfoDTO) {
        Route route = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
        Assert.isTrue(Objects.nonNull(route), "route can not null");

        if (StringUtils.isEmpty(interfaceInfoDTO.getAuthInfo())) {
            ServerHttpResponse response = exchange.getResponse();
            exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.GATEWAY_NOT_MATCH);
            response.setStatusCode(HttpStatus.UNAUTHORIZED);
            DataBuffer dataBuffer = response.bufferFactory().
                    wrap(StringUtil.convert2Bytes(
                            ResultVO.fail(HttpStatus.UNAUTHORIZED, "非本网关接口，拒绝访问")));
            response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
            return response.writeWith(Flux.just(dataBuffer));
        }

        List<AuthInfoDTO> gatewayDTOList = JSON.parseArray(interfaceInfoDTO.getAuthInfo(), AuthInfoDTO.class);
        AtomicBoolean authFlag = new AtomicBoolean(false);
        if (StringUtils.isEmpty(gatewayChannel)) {
            // 未配置不做网关匹配鉴权
            ExLogger.logger().error("未配置网关名称");
            return null;
        }
        Set<AuthGatewayEnum> gatewaySet = gatewayDTOList.stream().map(AuthInfoDTO::getAuthGateway).collect(Collectors.toSet());
        gatewaySet.forEach(gateway -> {
            if (gateway.name().equals(gatewayChannel)) {
                authFlag.set(true);
            }
        });
        if (!authFlag.get()) {
            ServerHttpResponse response = exchange.getResponse();
            response.setStatusCode(HttpStatus.UNAUTHORIZED);
            DataBuffer dataBuffer = response.bufferFactory().
                    wrap(StringUtil.convert2Bytes(
                            ResultVO.fail(HttpStatus.UNAUTHORIZED, "非本网关接口，拒绝访问")));
            response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
            return response.writeWith(Flux.just(dataBuffer));
        }
        return null;
    }

    // 拦截未授权接口
    private Mono<Void> interceptAppNotAuthReq(ServerWebExchange exchange, String appKey) {
        if (!isInterceptAppNotAuthReq()) {
            return null;
        }
        // 拦截应用未授权接口
        Route route = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
        Assert.isTrue(Objects.nonNull(route), "route can not null");
        String applicationName = route.getUri().getHost();
        ServerHttpRequest request = exchange.getRequest();
        String urlPath = HttpUtils.getGatewayStripPrefixPath(request.getURI().getPath());
        String method = request.getMethodValue();
        List<InterfaceInfoDTO> appAuthInterfaceList = safeCenterDataService.getAppAuthInterface(appKey, applicationName);

        // 匹配接口
        InterfaceInfoDTO matchInterfaceInfoDTO = InterfaceMatcherUtil.matchApi(appAuthInterfaceList, urlPath, method);
        if (matchInterfaceInfoDTO == null) {
            ServerHttpResponse response = exchange.getResponse();
            exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.AUTH_FAIL);
            return response(response, HttpStatus.UNAUTHORIZED, String.valueOf(RestCodeConstants.APP_NOT_AUTH_INTERFACE), "该应用无权访问该接口");
        } else {
            return null;
        }
    }


    // TODO 方法考虑统一 小程序鉴权错误码
    // 默认httpstatus 200
    protected Mono<Void> response(ServerHttpResponse response, String code, String msg) {
        return response(response, code, msg, null);
    }

    // TODO 方法考虑统一
    protected Mono<Void> response(ServerHttpResponse response, String code, String msg, Map<String, String> data) {
        return response(response, HttpStatus.OK, code, msg, data);
    }

    protected Mono<Void> response(ServerHttpResponse response, HttpStatus httpStatus, String code, String msg) {
        return response(response, httpStatus, code, msg, null);
    }

    protected Mono<Void> response(ServerHttpResponse response, HttpStatus httpStatus, String code, String msg, Map<String, String> data) {
        ResponseBase result = new ResponseBase<>();
        result.setCode(code);
        result.setMsg(msg);
        result.setData(data);
        response.setStatusCode(httpStatus);
        return writeJsonResponse(response, result);
    }

    private Mono<Void> writeJsonResponse(ServerHttpResponse response, Object body) {
        byte[] bytes = JSON.toJSONBytes(body);
        DataBuffer buffer = response.bufferFactory().wrap(bytes);
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON_UTF8);
        response.getHeaders().add("traceId", getTraceId());
        return response.writeWith(Mono.just(buffer));
    }

    private String getTraceId() {
        String traceId = TraceContext.traceId();
        if (StringUtils.isBlank(traceId) || "N/A".equalsIgnoreCase(traceId.trim())) {
            traceId = UUID.randomUUID().toString().replace("-", "");
        }
        return traceId;
    }

    /**
     * 是否拦截应用未授权接口
     * @return
     */
    protected abstract Boolean isInterceptAppNotAuthReq();

    // 接口鉴权
    protected abstract Mono<Void> auth(ServerWebExchange exchange, GatewayFilterChain chain);
}
