package com.yxt.safecenter.auth.sdk.enums;

/**
 * @version 1.0
 * @Description: 会员状态
 */
public enum MemberStatusEnum {
    REG(1, "已激活"),
    EXPIRED(2, "已注销"),
    FROZEN(3, "已冻结"),
    ;

    private Integer code;
    private String msg;

    MemberStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
