package com.yxt.safecenter.auth.sdk.dto.resp;


import lombok.Data;

/**
 * 接口接触返回对象
 */
@Data
public class ReturnData<E> {


    private E data;
    /**
     * 返回CODE
     */
    private Integer code;
    /**
     * 返回信息
     */
    private String msg;




    public static ReturnData getResult() {
        ReturnData returnData = new ReturnData<>();
        return returnData;
    }


    public ReturnData() {
        this.code = 0;
        this.msg = "请求成功!";
    }

    public ReturnData(E data) {
        this.code = 0;
        this.msg = "请求成功!";
        this.data = data;
    }

    public ReturnData(Integer code, String message) {
        this.code = code;
        this.msg = message;
    }

    public ReturnData(E data, Integer code, String message) {
        this.code = code;
        this.msg = message;
        this.data = data;
    }





}
