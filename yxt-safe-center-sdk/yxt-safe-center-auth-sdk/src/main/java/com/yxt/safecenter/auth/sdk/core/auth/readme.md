### 鉴权实现类bean名称规则：
#### 会话鉴权
bean名称：接口种设置的 `authMode`
#### 应用鉴权
bean名称：接口种设置的 `authMode` + `_` + 应用上配置的`authMethod`

### 新增鉴权方式
- 添加鉴权方式实现类
- [AuthMethodEnum.java](../../../../../../../../../../../../yxt-safe-center-common/yxt-safe-center-common-model/src/main/java/com/yxt/safecenter/common/model/enums/AuthMethodEnum.java)
、[AuthModeEnum.java](../../../../../../../../../../../../yxt-safe-center-common/yxt-safe-center-common-model/src/main/java/com/yxt/safecenter/common/model/enums/AuthModeEnum.java)
添加对应的枚举值