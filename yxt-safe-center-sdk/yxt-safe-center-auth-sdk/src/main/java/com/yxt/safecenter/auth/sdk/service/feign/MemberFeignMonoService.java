package com.yxt.safecenter.auth.sdk.service.feign;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.auth.sdk.dto.MemberInfoDTO;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * Since: 2025/04/24 20:32
 * Author: qs
 */

@Service
public class MemberFeignMonoService {

    @Resource
    private MemberFeignClient memberFeignClient;

    @Resource
    private Scheduler feignScheduler;

    public Mono<ResponseBase<MemberInfoDTO>> checkToken(String merCode, String token, Boolean containsFreeze) {
        return Mono.fromCallable(() -> memberFeignClient.checkToken(merCode, token, containsFreeze))
                .timeout(Duration.ofSeconds(5))
                .subscribeOn(feignScheduler);
    }
}
