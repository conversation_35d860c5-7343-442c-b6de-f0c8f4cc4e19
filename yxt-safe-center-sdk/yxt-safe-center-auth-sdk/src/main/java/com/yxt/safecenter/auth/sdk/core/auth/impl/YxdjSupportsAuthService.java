package com.yxt.safecenter.auth.sdk.core.auth.impl;

import com.yxt.lang.exception.YxtBaseException;
import com.yxt.safecenter.auth.sdk.config.MultipleWeeChatMerConfiguration;
import com.yxt.safecenter.auth.sdk.constants.AuthConstants;
import com.yxt.safecenter.auth.sdk.constants.Const;
import com.yxt.safecenter.auth.sdk.dto.MemberInfoDTO;
import com.yxt.safecenter.auth.sdk.enums.AuthFailStatusEnum;
import com.yxt.safecenter.auth.sdk.enums.PlatformEnum;
import com.yxt.safecenter.auth.sdk.service.feign.MemberFeignMonoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

import static com.yxt.safecenter.auth.sdk.constants.Const.*;

@Slf4j
@Service("SESSION_C_WEAK")
public class YxdjSupportsAuthService extends YxdjAbstractAuthService {

    //    @Resource
//    @Lazy
//    private MemberReactiveFeignClient memberReactiveFeignClient;
//    @Resource
//    private MemberServiceClient memberServiceClient;
    @Resource
    private MemberFeignMonoService memberFeignMonoService;


    @Resource
    @Lazy
    protected MultipleWeeChatMerConfiguration multipleWeeChatMerConfiguration;

    @Override
    protected Mono<Void> auth(ServerWebExchange exchange, GatewayFilterChain chain) {
        try {
            ServerHttpRequest request = exchange.getRequest();
            log.info("[Old-SupportsAuthInterceptor] RequestURI:" + request.getURI().getPath());
            ServerHttpRequest.Builder mutate = request.mutate();

            // 设置请求头不鉴权，网关处理过之后customer服务不处理
            mutate.headers(httpHeaders -> {
                httpHeaders.set(AuthConstants.OLD_AUTH_FLAG, Boolean.TRUE.toString());
            });
            PlatformEnum platform = getUserPlatform(request);
            if (Objects.nonNull(platform) && Const.MINI_PROGRAM.equals(platform.name())) {
                mutate.headers(httpHeaders -> {
                    httpHeaders.set(Const.HEAD_USER_ACCESS_CHANNEL, Const.MINI_PROGRAM);
                });
            }
            Optional<String> optional = getTokenFromRequest(request, USER_KEY);
            log.info("optional:{}", optional);
            String referer = request.getHeaders().getFirst("referer");
            log.info("referer:{}", referer);
            if (!optional.isPresent()) return chain.filter(exchange);

            String tokenKey = optional.get();

            return processNewTokenAuth(exchange, chain, tokenKey);
        } catch (YxtBaseException e) {
            return response(exchange.getResponse(), e.getCode(), e.getMessage());
        } catch (Exception e) {
            return chain.filter(exchange);
        }

    }

    private Mono<Void> processNewTokenAuth(ServerWebExchange exchange, GatewayFilterChain chain, String tokenKey) {
        ServerHttpRequest request = exchange.getRequest();
        String merCode = getMerCodeFromRequest(request);
        if (StringUtils.isEmpty(merCode)) {
            return response(exchange.getResponse(), "70016", "商户号不能为空");
        }
        return memberFeignMonoService.checkToken(merCode, tokenKey, true).flatMap(loginResponse -> {
            log.debug("MemberService login response code {} {} {}",
                    loginResponse.getCode(), loginResponse.getMsg(), loginResponse.getData());
            MemberInfoDTO memberInfoDTO;
            if (loginResponse.checkSuccess() && (memberInfoDTO = loginResponse.getData()) != null) {

                String userId = memberInfoDTO.getUserId().toString();
                PlatformEnum from = getUserAgent(request);
                String appId = getAppId(merCode, from, request);

                ServerHttpRequest.Builder mutate = request.mutate();
                mutate.headers(httpHeaders -> {
                    httpHeaders.set(AuthConstants.OLD_AUTH_FLAG, Boolean.FALSE.toString());
                    httpHeaders.set(Const.HEAD_USER_ID_KEY, userId);
                    httpHeaders.set(Const.HEAD_OPEN_ID_KEY, memberInfoDTO.getOpenIdBy(from.name(), appId));
                    httpHeaders.set(Const.HEAD_MEMBER_CARD_KEY, memberInfoDTO.getMemberCard());
                    httpHeaders.set(Const.HEAD_EMPLOYEE_FLAG, memberInfoDTO.getEmpFlag());
                });
                log.debug("User auth passed and loginInfo is userId={} tokenKey={} openId={}",
                        userId, tokenKey, loginResponse.getData().getOpenIdBy(from.name(), appId));
            }
            return chain.filter(exchange);
        }).onErrorResume(err -> {
            if (err instanceof YxtBaseException) {
                return response(exchange.getResponse(), ((YxtBaseException) err).getCode(), err.getMessage());
            } else {
                exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.AUTH_FAIL);
                return chain.filter(exchange);
            }
        });
    }

}
