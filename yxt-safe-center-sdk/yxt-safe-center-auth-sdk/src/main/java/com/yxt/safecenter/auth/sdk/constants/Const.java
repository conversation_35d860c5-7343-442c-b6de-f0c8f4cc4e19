package com.yxt.safecenter.auth.sdk.constants;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/6/21 11:05
 */
public class Const {
    public final static String NEED_PAY_RES_KEY = "need_pay_res_key";
    public final static String SERVICE_BASEINFO = "hydee-middle-baseinfo";

    public final static String METHOD_POST = "POST";
    public final static String CODE_REPEAT_MSG = "您的操作过于频繁，请稍后重试";

    public static final String USER_ZH_NAME_KEY = "baseinfo-user-zh-name";

    /**
     * 登录的有效用户ID
     */
    public static final String EFFECTIVE_LOGIN_USER_ID = "SYSTEM_EFFECTIVE_LOGIN_USER_ID";

    /**
     * 登录鉴权token缓存key
     */
    public static final String REDIS_OAUTH_LOGIN_TOKEN_KEY = "OAUTH:LOGIN:TOKEN:";

    /**
     * 是否校验token
     */
    public static final String CHECK_TOKEN_FLAG = "checkTokenFlag";

    public static final String MINI_USER_AGENT = "miniProgram";

    public static final String WX_USER_AGENT = "MicroMessenger";

    public static final String WX_ACCOUNT = "OFFIACCOUNT";

    public static final String H5 = "H5";

    public static final String MINI_PROGRAM = "WEAPP";

    public final static String USER_KEY = "user-key";

    public static final String MEMBER_SERVICE = "hydee-middle-member";

    public final static String HEAD_APPID_KEY = "appId";

    public static final String MARKET_APP_NAME = "hydee-middle-market";

    public static final String CUSTOMER_APP_NAME = "ydjia-merchant-customer";

    public static final String HEAD_USER_ACCESS_CHANNEL = "userAccessChannel";

    public final static String HEAD_USER_ID_KEY = "userId";

    public final static String HEAD_OPEN_ID_KEY = "openId";

    public final static String HEAD_UNION_ID_KEY = "unionId";

    public final static String HEAD_MEMBER_CARD_KEY = "memberCard";

    public final static String HEAD_EMPLOYEE_FLAG = "employeeFlag";

    /**
     * CookieRedis前缀
     */
    public static final String REDIS_BASE_PREFIX = "yxt:login:token:";

}
