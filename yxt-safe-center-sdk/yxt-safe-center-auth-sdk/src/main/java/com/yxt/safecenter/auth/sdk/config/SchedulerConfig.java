package com.yxt.safecenter.auth.sdk.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

/**
 * Since: 2025/2/14 14:08
 * Author: qs
 */

@Configuration
@EnableScheduling
public class SchedulerConfig {

    @Bean(destroyMethod = "dispose")
    public Scheduler feignScheduler(SafeCenterSchedulerProperties props) {
        return Schedulers.newBoundedElastic(
                props.getMaxThreads(),
                props.getMaxQueue(),
                props.getThreadNamePrefix()
        );
    }
}
