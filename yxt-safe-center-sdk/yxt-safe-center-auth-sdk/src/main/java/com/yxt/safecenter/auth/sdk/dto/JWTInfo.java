package com.yxt.safecenter.auth.sdk.dto;

import lombok.Data;


@Data
public class JWTInfo {

    private String userName;

    private String zhName;

    private String userId;

    private String merCode;

    private int exprie;
    /**
     * 员工编码
     * 员工账号才会不为空，其他账号类型可能为空
     */
    private String empCode;
    /**
     * 账号类型
     */
    private Integer accType;
    /**
     * 是否支持多端登录
     */
    private String multiLogin;


    public JWTInfo(String userId, String userName, String zhName, String merCode, String empCode, Integer accType, String multiLogin) {
        this.userName = userName;
        this.userId = userId;
        this.zhName = zhName;
        this.merCode = merCode;
        this.empCode = empCode;
        this.accType = accType;
        this.multiLogin = multiLogin;
    }

    public JWTInfo() {
    }
}
