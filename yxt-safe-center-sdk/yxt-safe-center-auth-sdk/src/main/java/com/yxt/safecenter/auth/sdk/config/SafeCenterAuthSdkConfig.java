package com.yxt.safecenter.auth.sdk.config;

import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import reactivefeign.spring.config.EnableReactiveFeignClients;

/**
 * Since: 2025/2/25 19:32
 * Author: qs
 */

@Configuration
@ComponentScan(basePackages = {"com.yxt.safecenter.auth.sdk"})
@EnableFeignClients(basePackages = "com.yxt.safecenter.auth.sdk.service.feign")
@EnableReactiveFeignClients(basePackages = "com.yxt.safecenter.auth.sdk.service.feign")
public class SafeCenterAuthSdkConfig {
}
