package com.yxt.safecenter.auth.sdk.utils;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/05/28 15:02
 */
public class RedisKeyUtil {

    private static final String REFERER_PREFIX = "referer:";

    private static final String MINI_PREFIX = "mini_program:";

    private static final String LIVE_CROSS_ADMIN_LOGIN_PREFIX = "live:cross_admin_login:";

    // 商品共享仓库存可售
    public static final String REDIS_B2C_EFFECTIVE_STOCK = "mall:b2cOrder:effectiveStock:";

    public static String getRefererCacheKey(String key) {
        return REFERER_PREFIX + key;
    }

    public static String getMiniCacheKey(String key) {
        return MINI_PREFIX + key;
    }

    public static String getCrossAdminLoginKey(String key) {
        return LIVE_CROSS_ADMIN_LOGIN_PREFIX + key;
    }

    public static String getRedisB2cEFFECTIVEStockKey(String merCode) {
        return REDIS_B2C_EFFECTIVE_STOCK + merCode;
    }

}
