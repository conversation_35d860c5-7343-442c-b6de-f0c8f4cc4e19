package com.yxt.safecenter.auth.sdk.utils;

import org.springframework.http.HttpCookie;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.MultiValueMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/16 18:16
 */
public class HttpUtils {

    private HttpUtils() {
    }

    public static String getCookieValueByName(ServerHttpRequest request, String name) {
        MultiValueMap<String, HttpCookie> cookies = request.getCookies();
        for (Map.Entry<String, List<HttpCookie>> entry : cookies.entrySet()) {
            String cookieName = entry.getKey();
            //从cookie中获取当前环境的token
            if ((name).equals(cookieName)) {
                return entry.getValue().get(0).getValue();
            }
        }
        return null;
    }

    /**
     * 获取网关接口中去掉路由前缀的路径
     * 网关stripPrefix后的路径
     * @param gatewayPath 网关路由过滤器获取到的原路径
     * @return 去除网关路由前缀的路径
     */
    public static String getGatewayStripPrefixPath(String gatewayPath) {
        String processedUrl = gatewayPath.startsWith("/") ? gatewayPath.substring(1) : gatewayPath;
        // 按第一个 "/" 分割为最多两部分
        String[] segments = processedUrl.split("/", 2);
        // 如果存在第二部分，则返回 "/" + 第二部分，否则返回 "/"
        return segments.length > 1 ? "/" + segments[1] : "/";
    }

}
