/**
 * 
 */
package com.yxt.safecenter.auth.sdk.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/** 
* @Description: 
* @author: frank
* @date: 2019年6月16日 下午4:42:30
*/


@Component
@ConfigurationProperties(prefix = "user-auth", ignoreUnknownFields = true)
@Data
public class SafeCenterUserAuthConfig {

	private boolean auth = true;
	
	private String ignoredPatterns = "";

	@Value("${bs.password:BS3u3aAVfC6epp}")
	private String tokenBs;

	private WhiteList whiteList = new WhiteList();

	@Data
	public static class WhiteList {
		private Map<String, Set<String>> systemMapIps = new HashMap<>();
	}
}
