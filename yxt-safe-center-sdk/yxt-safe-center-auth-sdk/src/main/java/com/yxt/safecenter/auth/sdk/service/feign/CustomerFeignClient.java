package com.yxt.safecenter.auth.sdk.service.feign;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.auth.sdk.constants.Const;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 微信开放平台接口
 * @create 2020/04/09 18:52
 */
@FeignClient(value = Const.CUSTOMER_APP_NAME)
public interface CustomerFeignClient {

    /**
     * 获取用户授权地址
     * @param merCode
     * @param referer 请求接口请求头中携带
     * @return
     */
    @GetMapping("/${api.version}/we-chat-open/oauth2buildAuthorizationUrl")
    ResponseBase<String> buildAuthorizationUrl(@RequestParam String merCode,
                                                 @RequestParam("referer") String referer);

}
