package com.yxt.safecenter.auth.sdk.job;

import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.auth.sdk.service.SafeCenterDataService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * 刷新安全中心数据
 * Since: 2025/2/25 16:49
 * Author: qs
 */

@Component
public class SafeCenterDataRefreshJob {

    @Resource
    private SafeCenterDataService safeCenterDataService;

    // 3分钟执行一次刷新数据
    @Scheduled(cron = "*/59 * * * * ?")
    public void executeTask() {
        ExLogger.logger("DataLoader").debug("刷新接口、应用数据开始");
        safeCenterDataService.refreshData(false);
    }
}
