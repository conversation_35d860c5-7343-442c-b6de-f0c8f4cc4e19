package com.yxt.safecenter.auth.sdk.job;

import com.yxt.safecenter.auth.sdk.dto.statistics.ApiStatisticsDTO;
import com.yxt.safecenter.auth.sdk.service.ApiStatisticsService;
import com.yxt.safecenter.auth.sdk.utils.LocalDateUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Since: 2025/2/14 14:11
 * Author: qs
 */

@Component
public class ApiStatisticsJob {

    @Resource
    private ApiStatisticsService apiStatisticsService;

    // 每小时执行一次
    @Scheduled(cron = "0 0 * * * ?")
    public void executeTask() {
        Map<String, ApiStatisticsDTO> apiStatisticsMap = apiStatisticsService.getApiStatisticsMap();

        // 过滤出小于当前时间(yyyyMMddHH)的 key
        String currentDateTime = LocalDateUtils.localDateTime2String(LocalDateTime.now(), LocalDateUtils.Pattern.YYYYMMDDHH);
        List<Map.Entry<String, ApiStatisticsDTO>> filteredEntries = apiStatisticsMap.entrySet().stream()
                .filter(entry -> {
                    String key = entry.getKey();
                    // 提取 yyyyMMddHH 部分
                    String keyDateTime = key.substring(0, 10);
                    // 比较 keyDateTime 与当前时间的大小
                    return keyDateTime.compareTo(currentDateTime) <= 0;
                })
                .collect(Collectors.toList());

        // 每 100 条数据为一批进行处理
        int batchSize = 100;
        for (int i = 0; i < filteredEntries.size(); i += batchSize) {
            List<Map.Entry<String, ApiStatisticsDTO>> batch = filteredEntries.subList(i, Math.min(i + batchSize, filteredEntries.size()));

            // 调用接口持久化数据，内存中的数据移除
            apiStatisticsService.sendBatchAndRemoveData(batch, apiStatisticsMap);
        }

    }

}
