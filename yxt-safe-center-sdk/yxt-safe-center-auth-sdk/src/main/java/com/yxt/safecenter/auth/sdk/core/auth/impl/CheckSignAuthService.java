package com.yxt.safecenter.auth.sdk.core.auth.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.auth.sdk.constants.AuthConstants;
import com.yxt.safecenter.auth.sdk.dto.AppConfigInterfaceDTO;
import com.yxt.safecenter.auth.sdk.enums.AuthFailStatusEnum;
import com.yxt.safecenter.auth.sdk.service.SafeCenterDataService;
import com.yxt.safecenter.auth.sdk.utils.GZIPUtils;
import com.yxt.safecenter.auth.sdk.utils.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage;
import org.springframework.cloud.gateway.support.BodyInserterContext;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * API网关种：CheckSignGatewayFilterFactory
 * rsa 使用merCode查询应用
 * Since: 2025/1/15 10:15
 * Author: qs
 */

@Slf4j
@Service("APP_RSA_AUTH_OLD")
public class CheckSignAuthService extends AbstractAuthService {


    private static final String DENY_KEYWORD = "actuator";

    private static final String DEFAULT_CHARSET = "UTF-8";

    private static final String SIGN_HEADER_PREFIX = "hydee-api-";

    private static final String SIGN_VALUE_PARAM_NAME = "sign";

    private static final String SIGN_TYPE_PARAM_NAME = "signType";

    private static final String SIGN_MER_CODE_PARAM_NAME = "merCode";

    private static final String SIGN_PARAM_NAMES_PARAM_NAME = "signParamNames";

    private static final String SIGN_TIMESTAMP_PARAM_NAME = "signTimestamp";

    private static final String CACHE_SIGN_ERROR_MSG_KEY = "signError";

    private static Set<String> signTypes = new HashSet<>();

    static {
        signTypes.add("RSA");
        signTypes.add("RSA2");
    }

    @Value("${signTtl:600000}")
    private Long signTtl;

    public void setSignTtl(Long signTtl) {
        this.signTtl = signTtl;
    }

    @Autowired
    private ServerCodecConfigurer serverCodecConfigurer;

    @Resource
    private SafeCenterDataService safeCenterDataService;

    @Override
    protected Boolean isInterceptAppNotAuthReq() {
        return Boolean.TRUE;
    }

    @Override
    public Mono<Void> auth(ServerWebExchange exchange, GatewayFilterChain chain) {

        ServerHttpResponse response = exchange.getResponse();
        ServerRequest serverRequest = ServerRequest.create(exchange, serverCodecConfigurer.getReaders());
        //ServerRequest serverRequest = ServerRequest.create(exchange, HandlerStrategies.withDefaults().messageReaders());

        String path = serverRequest.path();
        if (path.toLowerCase().contains(DENY_KEYWORD)) {
            return response(response, ErrorMsg.builder().msg(DENY_KEYWORD).build());
        }

        /*
         * 1. Get sign parameters from http header, check the sign if exists
         */
        Boolean checkSign = false;
        if (existsHeader(serverRequest)) {
            JSONObject jsonObject = buildSignJsonObjFromHeader(serverRequest);
            try {
                checkSign(jsonObject, response, true);
                //
                checkSign = true;
                //是否要走body解压逻辑
                if (serverRequest.headers().header("huditCompressed") == null
                        || serverRequest.headers().header("huditCompressed").size() <= 0
                        || !serverRequest.headers().header("huditCompressed").get(0).equals("1")) {
                    return chain.filter(exchange);
                }
                if (Objects.equals(serverRequest.method(), HttpMethod.GET)) {
                    //GET方法放行
                    return chain.filter(exchange);
                }
            } catch (Exception e) {
                String merCode = jsonObject.getString(SIGN_MER_CODE_PARAM_NAME);
                /*
                 *  get message
                 */
                String signErrorMsg = e.getMessage();
                if (signErrorMsg == null) {
                    signErrorMsg = "未知异常";
                    if (e.getCause() != null && e.getCause().getMessage() != null) {
                        signErrorMsg = e.getCause().getMessage();
                    }
                }

                /*
                 * log
                 */
                log.warn(signErrorMsg, e);
                String traceId = getTraceId();
                ExLogger.logger("AuditLog").field("OpenApi").field(merCode).field(serverRequest.path()).field(traceId).warn(signErrorMsg, e);
                exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.AUTH_FAIL);
                return response(response, ErrorMsg.builder().msg(signErrorMsg).traceId(traceId).build());
            }
        }



        /*
         * Get sign parameters from body
         */
        Boolean finalCheckSign = checkSign;
        Mono<String> modifiedBody = serverRequest.bodyToMono(String.class).flatMap(body -> {

            String merCode = null;

            try {

                //DataBuffer dataBuffer = DataBufferUtils.retain(body);
                //String bodyStr = new String(dataBuffer.asByteBuffer().array(), "UTF-8");
                try {
                    if (serverRequest.headers().header("huditCompressed") != null && serverRequest.headers().header("huditCompressed").size() > 0) {
                        String huditCompressed = serverRequest.headers().header("huditCompressed").get(0);
                        if (huditCompressed != null && huditCompressed.equals("1")) {
                            Integer leold = body.getBytes().length;
                            body = GZIPUtils.uncompressToString(body.getBytes(StandardCharsets.ISO_8859_1));
                            Integer lenew = body.getBytes().length;
                            try {
                                log.info("body接收大小：{}，解压后大小：{}，压缩比例：{}", leold, lenew, (100 - ((leold * 100) / lenew)) + "%");
                            } catch (Exception e) {
                            }
                        }
                    }
                } catch (Exception e) {
                    log.info("解压包体报错：{}", e);
                }
                Map<String, Object> bodyMap = JSON.parseObject(body);
                merCode = (String) bodyMap.get("merCode");
                if (!finalCheckSign) {
                    checkSign((JSONObject) bodyMap, response, false);
                }
                return Mono.just(body);
            } catch (Exception e) {

                /*
                 *  get message
                 */
                String signErrorMsg = e.getMessage();
                if (signErrorMsg == null) {
                    signErrorMsg = "未知异常";
                    if (e.getCause() != null && e.getCause().getMessage() != null) {
                        signErrorMsg = e.getCause().getMessage();
                    }
                }

                /*
                 * log
                 */
                log.warn(signErrorMsg, e);
                String traceId = getTraceId();
                ExLogger.logger("AuditLog").field("OpenApi").field(merCode).field(serverRequest.path()).field(traceId).warn(signErrorMsg, e);

                /*
                 * set into attributes
                 */
                exchange.getAttributes().put(CACHE_SIGN_ERROR_MSG_KEY, ErrorMsg.builder().msg(signErrorMsg).traceId(traceId).build());

                return Mono.just(body);
            } finally {
                //DataBufferUtils.release(body);
            }
        });

        BodyInserter bodyInserter = BodyInserters.fromPublisher(modifiedBody, String.class);
        HttpHeaders headers = new HttpHeaders();
        headers.putAll(exchange.getRequest().getHeaders());

        // the new content type will be computed by bodyInserter
        // and then set in the request decorator
        headers.remove(HttpHeaders.CONTENT_LENGTH);

        // if the body is changing content types, set it here, to the bodyInserter will know about it

        CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange, headers);
        return bodyInserter.insert(outputMessage, new BodyInserterContext())
                // .log("modify_request", Level.INFO)
                .then(Mono.defer(() -> {
                    ServerHttpRequestDecorator decorator = new ServerHttpRequestDecorator(
                            exchange.getRequest()) {
                        @Override
                        public HttpHeaders getHeaders() {
                            long contentLength = headers.getContentLength();
                            HttpHeaders httpHeaders = new HttpHeaders();
                            httpHeaders.putAll(super.getHeaders());
                            if (contentLength > 0) {
                                httpHeaders.setContentLength(contentLength);
                            } else {
                                // TODO: this causes a 'HTTP/1.1 411 Length Required' on httpbin.org
                                httpHeaders.set(HttpHeaders.TRANSFER_ENCODING, "chunked");
                            }
                            return httpHeaders;
                        }

                        @Override
                        public Flux<DataBuffer> getBody() {
                            return outputMessage.getBody();
                        }
                    };

                    ErrorMsg errorMsgObj = exchange.getAttribute(CACHE_SIGN_ERROR_MSG_KEY);
                    if (errorMsgObj != null) {
                        try {
                            return outputMessage.getBody().flatMap(dataBuffer -> {
                                DataBufferUtils.release(dataBuffer);
                                return response(response, errorMsgObj);
                            }).then();
                        } finally {
                            exchange.getAttributes().remove(CACHE_SIGN_ERROR_MSG_KEY);
                        }
                    }
                    return chain.filter(exchange.mutate().request(decorator).build());
                }));

    }


    private String getTraceId() {
        String traceId = TraceContext.traceId();
        if (StringUtils.isBlank(traceId) || "N/A".equalsIgnoreCase(traceId.trim())) {
            traceId = UUID.randomUUID().toString().replace("-", "");
        }
        return traceId;
    }

    private JSONObject buildSignJsonObjFromHeader(ServerRequest serverRequest) {
        String merCode = getHeader(serverRequest, SIGN_HEADER_PREFIX + SIGN_MER_CODE_PARAM_NAME);
        String sign = getHeader(serverRequest, SIGN_HEADER_PREFIX + SIGN_VALUE_PARAM_NAME);
        String signType = getHeader(serverRequest, SIGN_HEADER_PREFIX + SIGN_TYPE_PARAM_NAME);
        String signTimestamp = getHeader(serverRequest, SIGN_HEADER_PREFIX + SIGN_TIMESTAMP_PARAM_NAME);
        String signParameterNames = getHeader(serverRequest, SIGN_HEADER_PREFIX + SIGN_PARAM_NAMES_PARAM_NAME);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(SIGN_MER_CODE_PARAM_NAME, merCode);
        jsonObject.put(SIGN_VALUE_PARAM_NAME, sign);
        jsonObject.put(SIGN_TYPE_PARAM_NAME, signType);
        jsonObject.put(SIGN_TIMESTAMP_PARAM_NAME, signTimestamp);
        jsonObject.put(SIGN_PARAM_NAMES_PARAM_NAME, signParameterNames);

        return jsonObject;
    }

    private String getHeader(ServerRequest serverRequest, String headerName) {
        List<String> list = serverRequest.headers().header(headerName);
        return list.isEmpty() ? null : list.get(0);
    }

    private boolean existsHeader(ServerRequest serverRequest) {
        if (serverRequest == null) {
            return false;
        }
        List<String> merCodeHeaderList = serverRequest.headers().header(SIGN_HEADER_PREFIX + SIGN_MER_CODE_PARAM_NAME);
        List<String> signHeaderList = serverRequest.headers().header(SIGN_HEADER_PREFIX + SIGN_VALUE_PARAM_NAME);
        List<String> signTypeHeaderList = serverRequest.headers().header(SIGN_HEADER_PREFIX + SIGN_TYPE_PARAM_NAME);
        List<String> signNamesParamHeaderList = serverRequest.headers().header(SIGN_HEADER_PREFIX + SIGN_PARAM_NAMES_PARAM_NAME);
        List<String> signTimestampHeaderList = serverRequest.headers().header(SIGN_HEADER_PREFIX + SIGN_TIMESTAMP_PARAM_NAME);

        return !merCodeHeaderList.isEmpty()
                && !signHeaderList.isEmpty()
                && !signTypeHeaderList.isEmpty()
                && !signNamesParamHeaderList.isEmpty()
                && !signTimestampHeaderList.isEmpty();
    }

    private Mono<Void> response(ServerHttpResponse response, ErrorMsg errorMsgObj) {
        response.setStatusCode(HttpStatus.OK);
        Map<String, Object> result = new HashMap<>();
        result.put("code", HttpStatus.BAD_REQUEST.value());
        result.put("msg", errorMsgObj.getMsg());
        DataBuffer responseDataBuffer = response.bufferFactory().
                wrap(StringUtil.convert2Bytes(result));
        HttpHeaders responseHeaders = response.getHeaders();
        responseHeaders.add("Content-Type", "application/json;charset=UTF-8");
        responseHeaders.add("traceId", errorMsgObj.getTraceId());
        return response.writeWith(Flux.just(responseDataBuffer));
    }

    private Mono<Void> checkSign(JSONObject requestBodyJsonObject, ServerHttpResponse response, boolean isFromHeader) throws InvalidSignException {

        String paramPrefix = isFromHeader ? SIGN_HEADER_PREFIX : "";
        String tipMessagePrefix = isFromHeader ? "请求头" : "请求体";

        if (requestBodyJsonObject == null) {
            return this.handleError(null, response, HttpStatus.BAD_REQUEST, "错误原因: 请求体为空");
        }

        /*
         * Get "merCode", "sign", "signType", "signParamNames" from request body
         */
        String merCode = requestBodyJsonObject.getString(SIGN_MER_CODE_PARAM_NAME);
        if (StringUtils.isBlank(merCode)) {
            return this.handleError(null, response, HttpStatus.BAD_REQUEST, String.format("错误原因: " + tipMessagePrefix + "中缺少参数 %s", paramPrefix + SIGN_MER_CODE_PARAM_NAME));
        }

        String signValue = requestBodyJsonObject.getString(SIGN_VALUE_PARAM_NAME);
        if (StringUtils.isBlank(signValue)) {
            return this.handleError(merCode, response, HttpStatus.BAD_REQUEST, String.format("错误原因: " + tipMessagePrefix + "中缺少参数 %s", paramPrefix + SIGN_VALUE_PARAM_NAME));
        }

        String signType = requestBodyJsonObject.getString(SIGN_TYPE_PARAM_NAME);
        if (StringUtils.isBlank(signType)) {
            return this.handleError(merCode, response, HttpStatus.BAD_REQUEST, String.format("错误原因: " + tipMessagePrefix + "中缺少参数 %s", paramPrefix + SIGN_TYPE_PARAM_NAME));
        }
        if (!signTypes.contains(signType)) {
            return this.handleError(merCode, response, HttpStatus.BAD_REQUEST, String.format("错误原因: " + tipMessagePrefix + "中缺少参数 %s 不合法, 只支持 RSA 和 RSA2", paramPrefix + SIGN_TYPE_PARAM_NAME));
        }

        String signTimestampForSign = requestBodyJsonObject.getString(SIGN_TIMESTAMP_PARAM_NAME);
        if (StringUtils.isBlank(signTimestampForSign)) {
            return this.handleError(merCode, response, HttpStatus.BAD_REQUEST, String.format("错误原因: " + tipMessagePrefix + "中缺少参数 %s", paramPrefix + SIGN_TIMESTAMP_PARAM_NAME));
        }
        if (!NumberUtils.isCreatable(signTimestampForSign)) {
            return this.handleError(merCode, response, HttpStatus.BAD_REQUEST, String.format("错误原因: " + tipMessagePrefix + "中参数 %s 必须是数字", paramPrefix + SIGN_TIMESTAMP_PARAM_NAME));
        }
        long period = System.currentTimeMillis() - Long.parseLong(signTimestampForSign);
        if (Math.abs(period) > signTtl) {
            return this.handleError(merCode, response, HttpStatus.BAD_REQUEST, "错误原因: 签名已失效");
        }

        String parameterNamesForSign = requestBodyJsonObject.getString(SIGN_PARAM_NAMES_PARAM_NAME);
        if (StringUtils.isBlank(parameterNamesForSign)) {
            return this.handleError(merCode, response, HttpStatus.BAD_REQUEST, String.format("错误原因: " + tipMessagePrefix + "中缺少参数 %s", paramPrefix + SIGN_PARAM_NAMES_PARAM_NAME));
        }


        /*
         * Get
         */
        // TODO 获取应用配置信息，merCode兼容
        AppConfigInterfaceDTO appConfig = safeCenterDataService.getAppConfig(merCode);
        if (Objects.isNull(appConfig)) {
            return this.handleError(merCode, response, HttpStatus.NOT_FOUND, String.format("错误原因: MerCode %s 不存在或没有配置公钥", merCode));
        }
        String publicKey = appConfig.getPublicKey();


        /*
         * Build sign content
         */
        StringBuilder contentBuilder = new StringBuilder();
        String[] parameterNamesForSignArray = parameterNamesForSign.split(",");
        boolean hasSignTimestamp = false;
        for (int i = 0; i < parameterNamesForSignArray.length; i++) {
            String parameterName = parameterNamesForSignArray[i].trim();
            if (parameterName.equals(SIGN_TIMESTAMP_PARAM_NAME)) {
                hasSignTimestamp = true;
            }
            contentBuilder.append(parameterName);
            contentBuilder.append("=");
            String parameterValue;
            parameterValue = String.valueOf(requestBodyJsonObject.get(parameterName));
            contentBuilder.append(parameterValue);
            if (i < parameterNamesForSignArray.length - 1) {
                contentBuilder.append("&");
            }
        }
        if (!hasSignTimestamp) {
            return this.handleError(merCode, response, HttpStatus.BAD_REQUEST, String.format("错误原因: 参与签名的字段中必须包含%s", SIGN_TIMESTAMP_PARAM_NAME));
        }
        String contentForSign = contentBuilder.toString();


        /*
         * Check sign value
         */
        try {
            if (!AlipaySignature.rsaCheck(contentForSign, signValue, publicKey, DEFAULT_CHARSET, signType)) {
                return this.handleError(merCode, response, HttpStatus.BAD_REQUEST, "错误原因: 签名不正确");
            }
        } catch (AlipayApiException e) {
            log.warn("rsa check error! content: {}, sign: {}, publicKey: {}", contentForSign, signValue, publicKey, e);
            return this.handleError(merCode, response, HttpStatus.INTERNAL_SERVER_ERROR, "错误原因: 验证签名出现异常，签名不正确");
        }
        return null;
    }

    private Mono<Void> handleError(String merCode, ServerHttpResponse response, HttpStatus httpStatus, String message) throws InvalidSignException {
        throw new InvalidSignException(message);
//        response.setStatusCode(httpStatus);
//        DataBuffer dataBuffer = response.bufferFactory().
//                wrap(StringUtil.convert2Bytes(ResultVO.fail(httpStatus, message)));
//        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
//        return response.writeWith(Flux.just(dataBuffer));
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class ErrorMsg {

        private String msg;

        private String traceId;

    }

    private static class InvalidSignException extends Exception {

        InvalidSignException(String message) {
            super(message);
        }
    }
}
