package com.yxt.safecenter.auth.sdk.event.listener;

import com.alibaba.fastjson.JSON;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.auth.sdk.event.ApiStatisticsEvent;
import com.yxt.safecenter.auth.sdk.service.ApiStatisticsService;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * Since: 2025/2/13 10:01
 * Author: qs
 */

@Component
public class ApiStatisticsListener {

    @Resource
    private ApiStatisticsService apiStatisticsService;

    @EventListener
    @Async("apiStatisticsEventExecutor")
    public void handleApiStatisticsEvent(ApiStatisticsEvent event) {
        ExLogger.logger().debug("ApiStatisticsEvent 事件监听：{}", JSON.toJSONString(event));
        // 写入内存
        apiStatisticsService.saveMemory(event);
    }

}
