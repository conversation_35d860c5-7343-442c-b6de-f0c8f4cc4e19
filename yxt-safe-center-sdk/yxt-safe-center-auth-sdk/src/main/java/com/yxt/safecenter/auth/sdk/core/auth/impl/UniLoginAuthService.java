package com.yxt.safecenter.auth.sdk.core.auth.impl;

import com.alibaba.fastjson.JSONObject;
import com.yxt.lang.constants.INumberValue;
import com.yxt.lang.util.JsonUtils;
import com.yxt.safecenter.auth.sdk.config.GatewayExcludeUrlConfig;
import com.yxt.safecenter.auth.sdk.config.UserTokenAuthConfig;
import com.yxt.safecenter.auth.sdk.constants.CommonConstants;
import com.yxt.safecenter.auth.sdk.constants.Const;
import com.yxt.safecenter.auth.sdk.dto.*;
import com.yxt.safecenter.auth.sdk.dto.resp.ReturnData;
import com.yxt.safecenter.auth.sdk.dto.resp.TokenForbiddenResponse;
import com.yxt.safecenter.auth.sdk.dto.resp.UrlForbiddenResponse;
import com.yxt.safecenter.auth.sdk.utils.HyDeeRequestDecorator;
import com.yxt.safecenter.auth.sdk.utils.SafeCenterUserAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpCookie;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * Since: 2025/1/10 15:42
 * Author: qs
 */

@Slf4j
@Service("SESSION_B")
public class UniLoginAuthService extends AbstractAuthService {

    private static final String URL_SPLIT = "/";
    private static final String PATH_VARIABLE_SPLIT = "/{";

    protected final static String HEAD_USER_ID_KEY = "userId";
    private final static String HEAD_USER_NAME_KEY = "userName";

    /**
     * 用户中文名
     */
    private final static String HEAD_USER_ZH_NAME_KEY = "userZhName";
    /**
     * 员工编码标识
     */
    public static final String HEAD_EMP_CODE = "empCode";

    /**
     * 是否要重新校验token
     */
    public static final String RE_CHECK_TOKEN = "reCheckToken";
    /**
     * 账号类型标识
     */
    public static final String HEAD_ACC_TYPE = "accType";

    /**
     * 商户编码标识
     */
    protected final static String HEAD_USER_MERCODE = "merCode";

    @Value("${spring.profiles.active}")
    private String env;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired(required = false)
    // 不使用改类，无需配置RedisTemplate
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private GatewayExcludeUrlConfig gatewayExcludeUrlConfig;

    @Resource
    private SafeCenterUserAuthUtil safeCenterUserAuthUtil;

    @Value("${accessReleaseOnOff:false}")
    private Boolean accessReleaseOnOff;

    //针对哪些商户放行
    @Value("${token.resolver.igrone.mercodes:'SPHYDEE,hydee'}")
    private String HEAD_PASS_MERCODES = "SPHYDEE";

    @Value("${hyDeeRequestDecoratorReleaseOnOff:false}")
    private Boolean hyDeeRequestDecoratorReleaseOnOff;

    @Resource
    private UserTokenAuthConfig userTokenAuthConfig;

    @Override
    protected Boolean isInterceptAppNotAuthReq() {
        return Boolean.FALSE;
    }

    @Override
    public Mono<Void> auth(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String requestUri = request.getPath().pathWithinApplication().value();
        String method = request.getMethodValue();

        // 日志记录
        if (log.isDebugEnabled()) {
            log.debug("method:{} requestUri：{}", method, requestUri);
        }

        // 是否需要token鉴权
        if (Boolean.FALSE.equals(exchange.getAttributes().get(Const.CHECK_TOKEN_FLAG))) {
            return chain.filter(exchange);
        }

        exchange.getAttributes().put(RE_CHECK_TOKEN, Boolean.TRUE);
        try {
            ServerHttpRequest.Builder mutate = request.mutate();
            // 获取用户信息
            JWTInfo user = this.getJWTInfo(request, mutate);

            // 检查是否需要拦截
            if (gatewayExcludeUrlConfig.isStartWith(requestUri) || gatewayExcludeUrlConfig.isEndWith(requestUri)) {
                return permit(exchange, chain);
            }
            // 判断用户解析是否成功
            if (Objects.isNull(user)) {
                return getVoidMono(exchange, new TokenForbiddenResponse("User Token Forbidden or Expired!"));
            }
            // 判断用户是否有效
            if (!stringRedisTemplate.opsForSet().isMember(Const.EFFECTIVE_LOGIN_USER_ID, user.getUserId())) {
                return getVoidMono(exchange, new TokenForbiddenResponse("User Token Forbidden or Expired!"));
            }
            // 不再进行网关基础校验
            exchange.getAttributes().put(RE_CHECK_TOKEN, Boolean.FALSE);
            // 判断链接是否收费，不收费放行
            String matchPath = matchNeedPayPath(method, requestUri);
            if (StringUtils.isEmpty(matchPath)) {
                return permit(exchange, chain);
            }
            // 判断资源是否有效
            String userId = user.getUserId();
            if (!checkUserPermission(userId, matchPath)) {
                return getVoidMono(exchange, new UrlForbiddenResponse("User Forbidden!Does not has Permission!"));
            }
            ServerHttpRequest build = mutate.build();
            return chain.filter(exchange.mutate().request(build).build());
        } catch (Exception e) {
            //todo 上线后观察一段时间，如果服务没问题，比较稳定，error改成warn
            log.error("==== 执行YxtLoginAccess失败====，method:{} requestUri：{}", method, requestUri, e);
        }
        return permit(exchange, chain);
    }


    private JWTInfo getJWTInfo(ServerHttpRequest request, ServerHttpRequest.Builder mutate) {
        // cookie中获取不到，则会从请求头中获取Token
        String token = getToken(request);
        // 不存在token直接返回null
        if (StringUtils.isBlank(token)) {
            return null;
        }

        // 将token转换为解析对象
        String cookieRedisKey = Const.REDIS_BASE_PREFIX + this.hashToken(token);
        String tokenStr = stringRedisTemplate.opsForValue().get(cookieRedisKey);
        //token认证
        JWTInfo user;
        if (StringUtils.isNotBlank(tokenStr)) {
            TokenDTO UserDTO = JsonUtils.toObject(tokenStr, TokenDTO.class);
            user = this.convert2JWTInfo(UserDTO);
        } else {
            try {
                user = safeCenterUserAuthUtil.getInfoFromToken(token);
            } catch (Exception e) {
                return null;
            }
        }
        if (Objects.isNull(user)) {
            return null;
        }

        //如果是多端登录
        if (Objects.equals(user.getMultiLogin(), Boolean.FALSE.toString()) && StringUtils.isNotBlank(
                user.getUserId())) {
            Object loginTokenCache = stringRedisTemplate.opsForValue()
                    .get(Const.REDIS_OAUTH_LOGIN_TOKEN_KEY + user.getUserId());
            if (Objects.isNull(loginTokenCache)) {
                // log.warn("用户{}未登录", userId)Biz;
                return null;
            }
            if (!StringUtils.equals(token, loginTokenCache.toString())) {
                // log.warn("用户{}已在其他地方登录, 当前token已失效", userId);
                return null;
            }
        }

        // 设置用户header参数
        mutate.headers(httpHeaders -> {
            httpHeaders.set(HEAD_USER_ID_KEY, user.getUserId());
            httpHeaders.set(HEAD_USER_NAME_KEY, user.getUserName());
            httpHeaders.set(HEAD_EMP_CODE, user.getEmpCode());
            try {
                httpHeaders.set(HEAD_USER_ZH_NAME_KEY, URLEncoder.encode(user.getZhName(), "utf-8"));
            } catch (UnsupportedEncodingException e) {
                log.error("Encoding error: ", e);
                throw new RuntimeException(e);
            }
            httpHeaders.set(HEAD_ACC_TYPE, user.getAccType() == null ? "" : String.valueOf(user.getAccType()));
        });

        String headerMerCode = request.getHeaders().getFirst(HEAD_USER_MERCODE);
        String tokenMerCodee = user.getMerCode();
        log.debug("打印参数据{},{}", headerMerCode, tokenMerCodee);
        if (!ObjectUtils.isEmpty(headerMerCode) && !ObjectUtils.isEmpty(tokenMerCodee) && !headerMerCode.equals(
                tokenMerCodee)) {
            if (!HEAD_PASS_MERCODES.contains(tokenMerCodee)) {
                log.debug("商户token信息不匹配{},{}", headerMerCode, tokenMerCodee);
                return null;
            }
        }
        //登录强制重置header里面的商户号
        if (!ObjectUtils.isEmpty(tokenMerCodee) && !HEAD_PASS_MERCODES.contains(tokenMerCodee)) {
            List<String> merCodes = new ArrayList<>();
            merCodes.add(tokenMerCodee);
            mutate.headers(httpHeaders -> {
                httpHeaders.put(HEAD_USER_MERCODE, merCodes);
            });
        }
        setZhNameToRedis(user);
        return user;
    }

    private JWTInfo convert2JWTInfo(TokenDTO userDTO) {
        JWTInfo jwtInfo = new JWTInfo();
        jwtInfo.setUserName(userDTO.getUserName());
        jwtInfo.setZhName(userDTO.getZhName());
        jwtInfo.setMerCode(userDTO.getMerCode());
        jwtInfo.setUserId(userDTO.getUserId());
        jwtInfo.setExprie(userDTO.getExpire());
        jwtInfo.setEmpCode(userDTO.getEmpCode());
        jwtInfo.setAccType(Optional.ofNullable(userDTO.getAccType()).map(INumberValue::value).orElse(null));
        jwtInfo.setMultiLogin(String.valueOf(userDTO.getMultiLogin()));
        return jwtInfo;
    }

    private String getToken(ServerHttpRequest request) {
        MultiValueMap<String, HttpCookie> cookies = request.getCookies();
        for (Map.Entry<String, List<HttpCookie>> entry : cookies.entrySet()) {
            String cookieName = entry.getKey();
            //从cookie中获取当前环境的token
            if ((env + "_token").equals(cookieName) && CollectionUtils.isNotEmpty(entry.getValue())) {
                return Optional.ofNullable(entry.getValue().get(0)).map(HttpCookie::getValue).orElse(null);
            }
        }
        //cookie中获取不到，则采用老的方式从header中获取
        String authToken = request.getHeaders().getFirst(userTokenAuthConfig.getTokenHeader());
        if (StringUtils.isBlank(authToken)) {
            authToken = request.getQueryParams().getFirst("token");
        }
        return authToken;
    }

    private Mono<Void> permit(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String requestUri = request.getPath().pathWithinApplication().value();
        if (gatewayExcludeUrlConfig.isLogEndWith(requestUri) && Const.METHOD_POST.equals(request.getMethodValue())) {
            ServerHttpRequestDecorator decorator = new HyDeeRequestDecorator(request,
                    hyDeeRequestDecoratorReleaseOnOff);
            return chain.filter(exchange.mutate().request(decorator).build());
        }
        return chain.filter(exchange.mutate().request(request).build());
    }

    public static class Config {
        // 配置类，可以添加自定义配置属性，后续拓展可能会用到
    }

    /**
     * 生成字符串的md5
     *
     * @param str
     * @return java.lang.String
     * <AUTHOR>
     */
    public String hashToken(String str) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(str.getBytes());
            StringBuilder hexString = new StringBuilder();

            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 网关抛异常
     *
     * @param body 返回的数据
     */
    private Mono<Void> getVoidMono(ServerWebExchange serverWebExchange, ReturnData body) {
        serverWebExchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
        byte[] bytes = JSONObject.toJSONString(body).getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = serverWebExchange.getResponse().bufferFactory().wrap(bytes);
        try {
            return serverWebExchange.getResponse().writeWith(Flux.just(buffer));
        } finally {
            if (accessReleaseOnOff) {
                DataBufferUtils.release(buffer);
            }
        }
    }

    /**
     * 匹配当前资源是否是需要支付的资源
     *
     * @param path 访问路径
     * @return 找到匹配的资源
     * <AUTHOR>
     * @date 15:57 2019/7/30
     **/
    private String matchNeedPayPath(String method, String path) {
        SetOperations<String, Object> opsForSet = redisTemplate.opsForSet();
        Set<Object> members = opsForSet.members(Const.NEED_PAY_RES_KEY);
        if (members == null) {
            return null;
        }
        for (Object value : members) {
            String expect = (String) value;
            if (isRight(expect, method + path)) {
                return expect;
            }
        }
        return null;
    }

    /**
     * 实际访问路径是否与期望路径匹配
     *
     * @param respect 期望路径
     * @param actual  实际路径
     * @return boolean true-匹配，false-不匹配
     * <AUTHOR>
     * @date 15:44 2019/8/15
     **/
    private boolean isRight(String respect, String actual) {
        if (respect.contains(PATH_VARIABLE_SPLIT)) {
            return respect.split(URL_SPLIT).length == actual.split(URL_SPLIT).length && actual.startsWith(
                    respect.substring(0, respect.indexOf(PATH_VARIABLE_SPLIT)));
        } else {
            return actual.equals(respect);
        }
    }

    /**
     * 校验token是否过期，是否有url，是否过期
     *
     * @param matchPath 匹配上的路径
     * @return true 有权限，false-无权限
     */
    private boolean checkUserPermission(String userId, String matchPath) {
        HashOperations<String, String, Date> hashOperations = redisTemplate.opsForHash();
        Date date = hashOperations.get(CommonConstants.REDIS_PERMISSION_KEY + userId, matchPath);
        if (date == null) {
            return false;
        }
        return System.currentTimeMillis() <= date.getTime();
    }

    private void setZhNameToRedis(JWTInfo info) {
        HashOperations<String, String, String> hashOperations = stringRedisTemplate.opsForHash();
        hashOperations.put(Const.USER_ZH_NAME_KEY, info.getUserName(), info.getZhName());
    }

}
