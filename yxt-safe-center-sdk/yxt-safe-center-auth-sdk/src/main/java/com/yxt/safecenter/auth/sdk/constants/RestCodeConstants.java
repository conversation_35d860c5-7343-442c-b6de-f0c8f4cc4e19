package com.yxt.safecenter.auth.sdk.constants;


/**
 * 网关返回code值
 */
public class RestCodeConstants {

    public static final int TOKEN_ERROR_CODE = 40101;
    public static final int APP_NOT_AUTH_INTERFACE = 40110;
    public static final int TOKEN_FORBIDDEN_CODE = 40301;
    public final static int CODE_REPEAT = 40304;
    public static final int URL_FORBIDDEN_CODE = 40302;
    // 未维护必须配置
    public static final int URL_FORBIDDEN_NOT_CONFIG_CODE = 40305;
    // 接口未维护必须配置
    public static final int URL_FORBIDDEN_NOT_ONLINE_CODE = 40306;

    // 接口安全平台未匹配到接口
    public static final int URL_NOT_MATCH_CODE = 40401;

    // appKey为空
    public static final int BAD_REQUEST_NOT_APP_KEY_CODE = 40010;
}
