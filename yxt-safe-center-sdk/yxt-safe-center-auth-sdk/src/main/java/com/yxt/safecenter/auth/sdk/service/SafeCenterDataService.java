package com.yxt.safecenter.auth.sdk.service;

import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.auth.sdk.dto.AppConfigInterfaceDTO;
import com.yxt.safecenter.auth.sdk.dto.InterfaceInfoDTO;
import com.yxt.safecenter.auth.sdk.utils.BeanUtil;
import com.yxt.safecenter.auth.sdk.utils.PageUtils;
import com.yxt.safecenter.common.model.dto.req.SafeEnableAppConfigInterfacePageReq;
import com.yxt.safecenter.common.model.dto.resp.SafeAppConfigInterfaceApiResp;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceApiResp;
import com.yxt.safecenter.feign.sdk.api.SafeAppConfigApi;
import com.yxt.safecenter.feign.sdk.api.SafeInterfaceApi;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 数据加载刷新读取
 * Since: 2025/2/25 16:42
 * Author: qs
 */

@Service
public class SafeCenterDataService {

    @Resource
    @Lazy
    private GatewayRouteService gatewayRouteService;
    @Resource
    private SafeInterfaceApi safeInterfaceApi;
    @Resource
    private SafeAppConfigApi safeAppConfigApi;

    /**
     * 缓存接口数据：key 为 applicationName，value 为数据集合
     */
    private static final Map<String, List<InterfaceInfoDTO>> INTERFACE_CACHE = new HashMap<>();

    /**
     * 缓存应用配置数据：key 为 appKey，value 为 配置
     */
    private static final Map<String, AppConfigInterfaceDTO> APP_CONFIG_CACHE = new HashMap<>();

    /**
     * 加载刷新数据
     * 启动初始化数据加载数据异常要报错启动失败
     * 定时刷新有异常跳过加载后续数据
     * @param initData 是否启动首次初始化数据
     */
    public void refreshData(Boolean initData){
        // 路由中获取服务名
        List<String> applicationNameList = gatewayRouteService.getAppNames();

        boolean removeInterfaceCacheKey = true;
        if (CollectionUtils.isNotEmpty(applicationNameList)) {
            Set<String> newAppNameSet = new HashSet<>();
            for (String appName : applicationNameList) {
                // 加载接口数据
                try {
                    ResponseBase<List<SafeInterfaceApiResp>> responseBase = safeInterfaceApi.listByApplicationName(appName);
                    if (!responseBase.checkSuccess()) {
                        throw new YxtBizException(appName + "查询接口数据失败：" + responseBase.getMsg());
                    }
                    if (CollectionUtils.isNotEmpty(responseBase.getData())) {
                        INTERFACE_CACHE.put(appName, BeanUtil.copyList(responseBase.getData(), InterfaceInfoDTO.class));
                        newAppNameSet.add(appName);
                    }
                } catch (Exception e) {
                    // 有异常不确定拿到的是否完整数据，不能移除
                    removeInterfaceCacheKey = false;
                    ExLogger.logger("DataLoader").debug("查询服务{}接口信息失败：", appName, e);
                    // 如果接口数据加载失败，抛出异常使启动失败
                    if (initData) {
                        throw new YxtBizException(e.getMessage());
                    }
                }
            }

            // 移除没有的数据
            if (removeInterfaceCacheKey && !newAppNameSet.isEmpty()) {
                INTERFACE_CACHE.keySet().removeIf(appName -> !newAppNameSet.contains(appName));
            }

            AtomicBoolean removeAppCacheKey = new AtomicBoolean(true);
            Set<String> newAppKeySet = new HashSet<>();
            SafeEnableAppConfigInterfacePageReq appConfigPageReq = new SafeEnableAppConfigInterfacePageReq();
            PageUtils.pageQueryALL(
                    1,
                    200,
                    condition -> {
                        try {
                            appConfigPageReq.setPageSize(condition.getPageSize());
                            appConfigPageReq.setCurrentPage(condition.getCurrentPage());
                            ResponseBase<PageDTO<SafeAppConfigInterfaceApiResp>> pageDTOResponseBase = safeAppConfigApi.enableAppConfigPage(appConfigPageReq);
                            ExLogger.logger("DataLoader").debug("查询应用配置，params:{} ,result:{}", JSON.toJSONString(appConfigPageReq), JSON.toJSONString(pageDTOResponseBase));
                            if (pageDTOResponseBase == null) {
                                throw new YxtBizException("查询应用配置数据失败");
                            }
                            if (!pageDTOResponseBase.checkSuccess()) {
                                throw new YxtBizException("查询应用配置数据失败：" + pageDTOResponseBase.getMsg());
                            }
                            return pageDTOResponseBase.getData();
                        } catch (Exception e) {
                            // 有异常不确定拿到的是否完整数据，不能移除
                            removeAppCacheKey.set(false);
                            ExLogger.logger("DataLoader").error("查询应用配置数据失败：", e);
                            // 如果接口数据加载失败，抛出异常使启动失败
                            if (initData) {
                                throw new YxtBizException(e.getMessage());
                            }
                        }
                        return new PageDTO<>();
                    }, retList -> {
                        for (SafeAppConfigInterfaceApiResp appConfigResp : retList) {
                            APP_CONFIG_CACHE.put(appConfigResp.getAppKey(), BeanUtil.copyProperties(appConfigResp, AppConfigInterfaceDTO.class));
                            newAppKeySet.add(appConfigResp.getAppKey());
                        }
                    }
            );

            if (removeAppCacheKey.get() && !newAppKeySet.isEmpty()) {
                APP_CONFIG_CACHE.keySet().removeIf(appKey -> !newAppKeySet.contains(appKey));
            }
        }
    }

    // 获取服务接口数据
    public List<InterfaceInfoDTO> getInterfaceList(String applicationName) {
        return INTERFACE_CACHE.get(applicationName);
    }

    // 获取应用配置数据
    public AppConfigInterfaceDTO getAppConfig(String appKey) {
        return APP_CONFIG_CACHE.get(appKey);
    }

    /**
     * 获取应用授权接口
     * @param appKey 应用id
     * @param applicationName 服务名
     */
    public List<InterfaceInfoDTO> getAppAuthInterface(String appKey, String applicationName) {
        AppConfigInterfaceDTO appConfigInterfaceDTO = APP_CONFIG_CACHE.get(appKey);
        List<InterfaceInfoDTO> interfaceInfoDTOS = INTERFACE_CACHE.get(applicationName);
        return interfaceInfoDTOS.stream().filter(interfaceInfoDTO -> appConfigInterfaceDTO.getInterfaceList().contains(interfaceInfoDTO.getId())).collect(Collectors.toList());
    }
}
