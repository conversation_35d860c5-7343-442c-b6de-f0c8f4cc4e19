package com.yxt.safecenter.auth.sdk.service.feign;

import com.yxt.lang.dto.api.ResponseBase;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

import static com.yxt.safecenter.auth.sdk.constants.Const.MARKET_APP_NAME;

/**
* @author: JackLi
* @methodsName:
* @description: 开屏广告业务
* @param:
* @return:
* @throws:
**/
@FeignClient(value = MARKET_APP_NAME)
public interface ScreenAdvertFeignClient {

    /**
    * @author: JackLi
    * @methodsName: setPopFlag
    * @description: 设置弹广告标识
    * @param: [tokenStr, merCode]
    * @return: cn.hydee.starter.dto.ResponseBase<java.lang.Boolean>
    * @throws:
    **/
    @PostMapping("/${api.base-info-version}/screenAdvert/setPopFlag")
    ResponseBase<Boolean> setPopFlag(@RequestParam(value = "tokenStr")String tokenStr, @RequestParam(value = "merCode")String merCode);
}
