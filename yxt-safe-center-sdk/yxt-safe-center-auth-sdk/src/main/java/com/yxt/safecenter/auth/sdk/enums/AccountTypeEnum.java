package com.yxt.safecenter.auth.sdk.enums;

import com.yxt.lang.constants.IDescValue;
import com.yxt.lang.constants.INumberValue;

import java.util.Objects;

/**
 * 用于对账户类型的枚举，进行反序列化操作
 *
 * <AUTHOR>
 */
public enum AccountTypeEnum implements INumberValue<Integer>, IDescValue {

    EMPLOYEE(0, "员工账户"),
    STORE(1, "门店账户"),
    ;

    private final Integer value;
    private final String desc;

    AccountTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @Override
    public String desc() {
        return desc;
    }

    @Override
    public Integer value() {
        return value;
    }

    @Override
    public boolean isSame(Integer value) {
        return Objects.equals(value, this.value);
    }
}
