package com.yxt.safecenter.auth.sdk.utils;

import com.yxt.lang.dto.PageBase;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.util.ExLogger;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * Since: 2025/2/25 15:16
 * Author: qs
 */
public class PageUtils {

    //分页查询所有数据，直到查询完成
    public static <IN extends PageBase, OUT> void pageQueryALL(int startPage, int pageSize, QueryFunction<IN, OUT> queryFunction, Handler<OUT> handler){
        if (queryFunction == null || handler == null){
            return;
        }
        startPage = startPage <= 0 ? 1 : startPage;
        pageSize = pageSize <= 0 || pageSize > 10000 ? 10 : pageSize;
        int count = Integer.MAX_VALUE;
        IN pageBase = (IN) new PageBase();
        pageBase.setPageSize((long) pageSize);
        pageBase.setCurrentPage((long) startPage);
        while ((startPage-1)*pageSize < count){
            PageDTO<OUT> queryResult = queryFunction.query(pageBase);
            try {
                if (queryResult == null){
                    break;
                }
                if (CollectionUtils.isNotEmpty(queryResult.getData())) {
                    handler.handle(queryResult.getData());
                }
            }catch (Exception e){
                ExLogger.logger("queryAll").error("PageQueryAll执行出错！", e);
            }
            count = queryResult.getTotalCount() != null ? queryResult.getTotalCount().intValue() : 0;
            pageBase.setPageSize((long) pageSize);
            pageBase.setCurrentPage((long) ++startPage);
        }
    }

    public interface QueryFunction<IN extends PageBase, OUT>{
        PageDTO<OUT> query(IN condition);
    }

    public interface Handler<T>{
        void handle(List<T> list);
    }

}
