package com.yxt.safecenter.auth.sdk.core.auth;

import com.yxt.safecenter.auth.sdk.dto.InterfaceInfoDTO;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * Since: 2025/1/10 14:23
 * Author: qs
 */
public interface AuthService {

    /**
     * 执行鉴权逻辑
     * @param interfaceInfoDTO 接口信息
     * @param appKey 应用编码
     * @return mono
     */
    Mono<Void> execute(ServerWebExchange exchange, GatewayFilterChain chain, InterfaceInfoDTO interfaceInfoDTO, String appKey);

}
