package com.yxt.safecenter.auth.sdk.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Since: 2025/1/13 17:26
 * Author: qs
 */

@Data
public class AppConfigInterfaceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 应用id
     */
    private String appKey;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 使用非对称方式鉴权密钥、TOKEN等
     */
    private String cerKey;

    /**
     * 鉴权方式 RSA-rsa/rsa2验签 MD5-md5验签 TOKENBS-验证token是否相同的特殊方式
     */
    private String authMethod;

    /**
     * 生效时间
     */
    private LocalDateTime startTime;

    /**
     * 失效时间
     */
    private LocalDateTime endTime;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 应用授权接口ID列表
     */
    List<Long> interfaceList;

}
