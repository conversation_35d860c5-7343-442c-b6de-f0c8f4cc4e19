package com.yxt.safecenter.auth.sdk.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Since: 2025/1/7 15:17
 * Author: qs
 */

@Data
public class InterfaceInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 服务名
     */
    private String applicationName;

    /**
     * API方法
     */
    private String apiMethod;

    /**
     * 请求方式
     */
    private String apiWay;

    /**
     * path路径
     */
    private String apiPath;

    /**
     * 权限相关 json
     */
    private String authInfo;

    /**
     * 是否统一加解密
     */
    private String encryptionRequired;

    /**
     * 状态
     */
    private String status;

}
