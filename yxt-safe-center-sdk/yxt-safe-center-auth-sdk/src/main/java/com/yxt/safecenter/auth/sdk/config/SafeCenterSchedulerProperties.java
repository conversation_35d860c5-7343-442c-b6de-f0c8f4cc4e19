package com.yxt.safecenter.auth.sdk.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Since: 2025/05/07 10:47
 * Author: qs
 */

@Configuration
@ConfigurationProperties(prefix = "safe-center.scheduler")
@Data
public class SafeCenterSchedulerProperties {

    /** 最大线程数 */
    private int maxThreads = 500;
    /** 最大队列长度 */
    private int maxQueue = 10000;
    /** 线程名前缀 */
    private String threadNamePrefix = "feign-blocking-pool";

}
