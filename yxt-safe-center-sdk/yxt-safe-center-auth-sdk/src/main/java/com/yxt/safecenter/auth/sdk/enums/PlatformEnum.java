package com.yxt.safecenter.auth.sdk.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description 登录平台
 * @Date 2020/3/24
 * @Param
 * @return
 */
@Getter
public enum PlatformEnum {

    WEAPP(2, "微信小程序"),
    H5(3, "H5"),
    OFFIACCOUNT(1, "公众号");

    private Integer code;
    private String message;

    PlatformEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static PlatformEnum codeOf(Integer code) {
        for (PlatformEnum s : PlatformEnum.values()) {
            if (s.getCode().equals(code))
                return s;
        }
        return null;
    }

    public static boolean isWeeAppByEnumName(String name) {
        return WEAPP.name().equals(name);
    }

}
