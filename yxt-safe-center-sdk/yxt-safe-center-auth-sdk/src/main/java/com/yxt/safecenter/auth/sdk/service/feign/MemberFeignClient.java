package com.yxt.safecenter.auth.sdk.service.feign;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.auth.sdk.dto.MemberInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

import static com.yxt.safecenter.auth.sdk.constants.Const.MEMBER_SERVICE;

/**
 * Since: 2025/1/22 17:07
 * Author: qs
 */

@FeignClient(value = MEMBER_SERVICE)
public interface MemberFeignClient {

    /**
     * 检查用户token(是否登录)
     * @param token
     * @return
     */
    @PostMapping("/${api.version}/memberlogin/checkToken")
    ResponseBase<MemberInfoDTO> checkToken(@RequestParam String merCode, @RequestParam String token, @RequestParam(required = false) Boolean containsFreeze);

}
