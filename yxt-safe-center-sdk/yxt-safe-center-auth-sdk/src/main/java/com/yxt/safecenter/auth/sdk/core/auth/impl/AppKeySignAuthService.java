package com.yxt.safecenter.auth.sdk.core.auth.impl;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.auth.sdk.constants.AuthConstants;
import com.yxt.safecenter.auth.sdk.dto.AppConfigInterfaceDTO;
import com.yxt.safecenter.auth.sdk.enums.AuthFailStatusEnum;
import com.yxt.safecenter.auth.sdk.service.SafeCenterDataService;
import com.yxt.safecenter.auth.sdk.utils.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;

/**
 * api网关中：AppKeyGatewayFilterFactory
 * Since: 2025/1/13 16:46
 * Author: qs
 */

@Slf4j
@Service("APP_RSA_AUTH")
public class AppKeySignAuthService extends AbstractAuthService {

    private static final String DENY_KEYWORD = "actuator";
    private static final String DEFAULT_CHARSET = "UTF-8";
    private static final String SIGN_HEADER_PREFIX = "";
    private static final String APPKEY = "appKey";
    private static final String SIGN_VALUE_PARAM_NAME = "sign";
    private static final String SIGN_TYPE_PARAM_NAME = "signType";
    private static final String SIGN_MER_CODE_PARAM_NAME = "merCode";
    private static final String SIGN_PARAM_NAMES_PARAM_NAME = "signParamNames";
    private static final String SIGN_TIMESTAMP_PARAM_NAME = "signTimestamp";
    /* 原有代码定义的关于压缩的headerName*/

    private static final Set<String> signTypes = new HashSet<>();

    static {
        signTypes.add("RSA");
        signTypes.add("RSA2");
    }

    @Value("${signTtl:600000}")
    private Long signTtl;

    @Resource
    private ServerCodecConfigurer serverCodecConfigurer;
    @Resource
    private SafeCenterDataService safeCenterDataService;

    @Override
    protected Boolean isInterceptAppNotAuthReq() {
        return Boolean.TRUE;
    }

    @Override
    public Mono<Void> auth(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpResponse response = exchange.getResponse();
        ServerRequest serverRequest = ServerRequest.create(exchange, serverCodecConfigurer.getReaders());
        String path = serverRequest.path();
        if (path.toLowerCase().contains(DENY_KEYWORD)) {
            return response(response, ErrorMsg.builder().msg(DENY_KEYWORD).build());
        }

        /*
         * 1. Get sign parameters from http header, check the sign if exists
         */
        // 必须要有请求头
        if (!existsHeader(serverRequest)) {
            return response(response, ErrorMsg.builder().msg("错误原因: 请求头为空").traceId(getTraceId()).build());
        }
        // 解析请求头，并校验
        JSONObject jsonObject = buildSignJsonObjFromHeader(serverRequest);
        try {
            // 最终都要通过请求头中获取签名参数
            checkSign(jsonObject);
        } catch (Exception e) {
            String appKey = jsonObject.getString(APPKEY);
            /*
             *  get message
             */
            String signErrorMsg = e.getMessage();
            if (signErrorMsg == null) {
                signErrorMsg = "未知异常";
                if (e.getCause() != null && e.getCause().getMessage() != null) {
                    signErrorMsg = getCause(e).getMessage();
                }
            }
            /*
             * log
             */
            log.warn(signErrorMsg, e);
            String traceId = getTraceId();
            ExLogger.logger("AuditLog").field("OpenApi").field(appKey).field(serverRequest.path()).field(traceId).warn(signErrorMsg, e);
            exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.AUTH_FAIL);
            return response(response, ErrorMsg.builder().msg(signErrorMsg).traceId(traceId).build());
        }
        return chain.filter(exchange);
    }


    private static Throwable getCause(Exception e) {
        return e.getCause();
    }

    private String getTraceId() {
        String traceId = TraceContext.traceId();
        if (StringUtils.isBlank(traceId) || "N/A".equalsIgnoreCase(traceId.trim())) {
            traceId = UUID.randomUUID().toString().replace("-", "");
        }
        return traceId;
    }

    private JSONObject buildSignJsonObjFromHeader(ServerRequest serverRequest) {
        String appKey = getHeader(serverRequest, APPKEY);
        String sign = getHeader(serverRequest, SIGN_HEADER_PREFIX + SIGN_VALUE_PARAM_NAME);
        String signType = getHeader(serverRequest, SIGN_HEADER_PREFIX + SIGN_TYPE_PARAM_NAME);
        String signTimestamp = getHeader(serverRequest, SIGN_HEADER_PREFIX + SIGN_TIMESTAMP_PARAM_NAME);
        String signParameterNames = getHeader(serverRequest, SIGN_HEADER_PREFIX + SIGN_PARAM_NAMES_PARAM_NAME);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(APPKEY, appKey);
        jsonObject.put(SIGN_VALUE_PARAM_NAME, sign);
        jsonObject.put(SIGN_TYPE_PARAM_NAME, signType);
        jsonObject.put(SIGN_TIMESTAMP_PARAM_NAME, signTimestamp);
        jsonObject.put(SIGN_PARAM_NAMES_PARAM_NAME, signParameterNames);

        return jsonObject;
    }

    private String getHeader(ServerRequest serverRequest, String headerName) {
        List<String> list = serverRequest.headers().header(headerName);
        return list.isEmpty() ? null : list.get(0);
    }

    private boolean existsHeader(ServerRequest serverRequest) {
        if (Objects.isNull(serverRequest)) {
            return false;
        }
        List<String> signHeaderList = serverRequest.headers().header(SIGN_HEADER_PREFIX + SIGN_VALUE_PARAM_NAME);
        List<String> signTypeHeaderList = serverRequest.headers().header(SIGN_HEADER_PREFIX + SIGN_TYPE_PARAM_NAME);
        List<String> signNamesParamHeaderList = serverRequest.headers().header(SIGN_HEADER_PREFIX + SIGN_PARAM_NAMES_PARAM_NAME);
        List<String> signTimestampHeaderList = serverRequest.headers().header(SIGN_HEADER_PREFIX + SIGN_TIMESTAMP_PARAM_NAME);
        List<String> appKeyHeaderList = serverRequest.headers().header(APPKEY);

        return !signHeaderList.isEmpty()
                && !signTypeHeaderList.isEmpty()
                && !signNamesParamHeaderList.isEmpty()
                && !signTimestampHeaderList.isEmpty()
                && !appKeyHeaderList.isEmpty();
    }

    private Mono<Void> response(ServerHttpResponse response, ErrorMsg errorMsgObj) {
        response.setStatusCode(HttpStatus.OK);
        Map<String, Object> result = new HashMap<>();
        result.put("code", HttpStatus.BAD_REQUEST.value());
        result.put("msg", errorMsgObj.getMsg());
        DataBuffer responseDataBuffer = response.bufferFactory().
                wrap(StringUtil.convert2Bytes(result));
        HttpHeaders responseHeaders = response.getHeaders();
        responseHeaders.add("Content-Type", "application/json;charset=UTF-8");
        responseHeaders.add("traceId", errorMsgObj.getTraceId());
        return response.writeWith(Flux.just(responseDataBuffer));
    }

    /**
     * 签名校验
     */
    private void checkSign(JSONObject requestBodyJsonObject) throws InvalidSignException {

        if (Objects.isNull(requestBodyJsonObject)) {
            handleError(false, null, HttpStatus.BAD_REQUEST, "错误原因: 请求头为空");
        }

        /*
         * Get "appKey", "sign", "signType", "signParamNames" from request body
         */
        String appKey = requestBodyJsonObject.getString(APPKEY);
        if (StringUtils.isBlank(appKey)) {
            handleError(false, null, HttpStatus.BAD_REQUEST, String.format("错误原因: 请求头中缺少参数 %s", SIGN_MER_CODE_PARAM_NAME));
        }

        String signValue = requestBodyJsonObject.getString(SIGN_VALUE_PARAM_NAME);
        if (StringUtils.isBlank(signValue)) {
            handleError(false, appKey, HttpStatus.BAD_REQUEST, String.format("错误原因: 请求头中缺少参数 %s", SIGN_VALUE_PARAM_NAME));
        }

        String signType = requestBodyJsonObject.getString(SIGN_TYPE_PARAM_NAME);
        if (StringUtils.isBlank(signType)) {
            handleError(false, appKey, HttpStatus.BAD_REQUEST, String.format("错误原因: 请求头中缺少参数 %s", SIGN_TYPE_PARAM_NAME));
        }
        if (!signTypes.contains(signType)) {
            handleError(false, appKey, HttpStatus.BAD_REQUEST, String.format("错误原因: 请求头中缺少参数 %s 不合法, 只支持 RSA 和 RSA2", SIGN_TYPE_PARAM_NAME));
        }

        String signTimestampForSign = requestBodyJsonObject.getString(SIGN_TIMESTAMP_PARAM_NAME);
        if (StringUtils.isBlank(signTimestampForSign)) {
            handleError(false, appKey, HttpStatus.BAD_REQUEST, String.format("错误原因: 请求头中缺少参数 %s", SIGN_TIMESTAMP_PARAM_NAME));
        }
        if (!NumberUtils.isCreatable(signTimestampForSign)) {
            handleError(false, appKey, HttpStatus.BAD_REQUEST, String.format("错误原因: 请求头中参数 %s 必须是数字", SIGN_TIMESTAMP_PARAM_NAME));
        }
        long period = System.currentTimeMillis() - Long.parseLong(signTimestampForSign);
        if (Math.abs(period) > signTtl) {
            handleError(false, appKey, HttpStatus.BAD_REQUEST, "错误原因: 签名已失效");
        }

        String parameterNamesForSign = requestBodyJsonObject.getString(SIGN_PARAM_NAMES_PARAM_NAME);
        if (StringUtils.isBlank(parameterNamesForSign)) {
            handleError(false, appKey, HttpStatus.BAD_REQUEST, String.format("错误原因: 请求头中缺少参数 %s", SIGN_PARAM_NAMES_PARAM_NAME));
        }

        /*
         * Get App key
         */
        AppConfigInterfaceDTO appConfig = safeCenterDataService.getAppConfig(appKey);
        if (Objects.isNull(appConfig)) {
            handleError(false, appKey, HttpStatus.NOT_FOUND, String.format("错误原因: AppKey %s 不存在或没有配置公钥!", appKey));
        }
        String publicKey = appConfig.getPublicKey();


        /*
         * Build sign content
         */
        StringBuilder contentBuilder = new StringBuilder();
        String[] parameterNamesForSignArray = parameterNamesForSign.split(",");
        boolean hasSignTimestamp = false;
        for (int i = 0; i < parameterNamesForSignArray.length; i++) {
            String parameterName = parameterNamesForSignArray[i].trim();
            if (parameterName.equals(SIGN_TIMESTAMP_PARAM_NAME)) {
                hasSignTimestamp = true;
            }
            contentBuilder.append(parameterName);
            contentBuilder.append("=");
            String parameterValue;
            parameterValue = String.valueOf(requestBodyJsonObject.get(parameterName));
            contentBuilder.append(parameterValue);
            if (i < parameterNamesForSignArray.length - 1) {
                contentBuilder.append("&");
            }
        }
        if (!hasSignTimestamp) {
            handleError(false, appKey, HttpStatus.BAD_REQUEST, String.format("错误原因: 参与签名的字段中必须包含%s", SIGN_TIMESTAMP_PARAM_NAME));
        }
        String contentForSign = contentBuilder.toString();


        /*
         * Check sign value
         */
        try {
            if (!AlipaySignature.rsaCheck(contentForSign, signValue, publicKey, DEFAULT_CHARSET, signType)) {
                handleError(false, appKey, HttpStatus.BAD_REQUEST, "错误原因: 签名不正确");
            }
        } catch (AlipayApiException e) {
            log.warn("rsa check error! content: {}, sign: {}, publicKey: {}", contentForSign, signValue, publicKey, e);
            handleError(true, appKey, HttpStatus.INTERNAL_SERVER_ERROR, "错误原因: 验证签名出现异常，签名不正确");
        }
    }

    private void handleError(Boolean isLog, String appKey, HttpStatus httpStatus, String message) throws InvalidSignException {
        if (isLog) {
            log.info("CheckSign error, appKey:{}, httpStatus:{}, message:{}", appKey, httpStatus, message);
        }
        throw new InvalidSignException(message);
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class ErrorMsg {

        private String msg;

        private String traceId;

    }

    private static class InvalidSignException extends Exception {
        InvalidSignException(String message) {
            super(message);
        }
    }
}
