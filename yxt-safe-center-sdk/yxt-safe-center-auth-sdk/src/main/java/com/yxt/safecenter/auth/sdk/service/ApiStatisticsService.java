package com.yxt.safecenter.auth.sdk.service;

import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.auth.sdk.dto.statistics.ApiStatisticsDTO;
import com.yxt.safecenter.auth.sdk.event.ApiStatisticsEvent;
import com.yxt.safecenter.common.model.dto.req.ApiCallStatisticsReq;
import com.yxt.safecenter.feign.sdk.api.ApiStatisticsApi;
import lombok.Getter;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Since: 2025/2/13 11:35
 * Author: qs
 */

@Service
public class ApiStatisticsService {

    // 获取所有的统计数据
    @Getter
    private final Map<String, ApiStatisticsDTO> apiStatisticsMap = new ConcurrentHashMap<>();

    @Resource
    private ApiStatisticsApi apiStatisticsApi;

    public void saveMemory(ApiStatisticsEvent apiStatistics) {
        String key = apiStatistics.genKey();
        String period = apiStatistics.getPeriod(key);
        ApiStatisticsDTO apiStatisticsDTO = apiStatisticsMap.computeIfAbsent(key, k ->
                        new ApiStatisticsDTO(apiStatistics.getApplicationName(), apiStatistics.getApiPath(), apiStatistics.getApiMethod()
                                , apiStatistics.getGateway(), apiStatistics.getAppKey(), apiStatistics.getAuthMode(), apiStatistics.getAppAuthMethod(), period));
        apiStatisticsDTO.incrementTotalCalls();
        if (apiStatistics.getAuthFailStatusEnum() != null) {
            switch (apiStatistics.getAuthFailStatusEnum()) {
                case GATEWAY_NOT_MATCH:
                    apiStatisticsDTO.incrementChannelInterceptions();
                    break;
                case RBAC_FAIL:
                    apiStatisticsDTO.incrementRbacInterceptions();
                    break;
                case AUTH_FAIL:
                    apiStatisticsDTO.incrementAuthInterceptions();
                    break;
            }
        }
        // TODO 加入清理逻辑，避免内存溢出
    }

    public void sendBatchAndRemoveData(List<Map.Entry<String, ApiStatisticsDTO>> batch, Map<String, ApiStatisticsDTO> apiStatisticsMap) {

        // 将 ApiStatisticsDTO 转换为 ApiStatisticsReq
        List<ApiCallStatisticsReq> requestList = batch.stream()
                .map(entry -> {
                    ApiStatisticsDTO dto = entry.getValue();
                    ApiCallStatisticsReq req = new ApiCallStatisticsReq();
                    BeanUtils.copyProperties(dto, req);
                    return req;
                })
                .collect(Collectors.toList());

        // 调用 Feign 接口发送数据
        try {
            apiStatisticsApi.apiStatisticsSaveBatch(requestList);

            // 数据发送成功后，从原 map 中移除对应的 key
            for (Map.Entry<String, ApiStatisticsDTO> entry : batch) {
                String key = entry.getKey();
                apiStatisticsMap.remove(key);
            }
        } catch (Exception e) {
            ExLogger.logger("sendBatchAndRemoveData").error("调用api/interface/statistics接口异常", e);
        }
    }

}

