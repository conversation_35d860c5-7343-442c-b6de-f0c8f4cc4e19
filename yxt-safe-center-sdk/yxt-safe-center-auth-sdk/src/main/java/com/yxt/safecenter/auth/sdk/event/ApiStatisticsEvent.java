package com.yxt.safecenter.auth.sdk.event;

import com.yxt.safecenter.auth.sdk.enums.AuthFailStatusEnum;
import com.yxt.safecenter.auth.sdk.utils.LocalDateUtils;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * Since: 2025/2/12 16:07
 * Author: qs
 */

public class ApiStatisticsEvent extends ApplicationEvent {

    /**
     * 服务名
     */
    @Getter
    private String applicationName;

    /**
     * 实际请求path路径，只有一个值
     */
    @Getter
    private String apiPath;

    /**
     * 实际请求方式，只有一个值
     */
    @Getter
    private String apiMethod;

    /**
     * 实际调用网关 C-小程序网关 API-对外网关  BIGDATA-大数据网关 BUS-B端用户网关
     */
    @Getter
    private String gateway;

    /**
     * 调用的appKey
     */
    @Getter
    private String appKey;

    /**
     * 调用的鉴权模式 SESSION-会话 SIGN-验签 为空不鉴权
     */
    @Getter
    private String authMode;

    /**
     * 应用鉴权方式
     */
    @Getter
    private String appAuthMethod;

    /**
     * 鉴权失败状态
     */
    @Getter
    private AuthFailStatusEnum authFailStatusEnum;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     * @param applicationName            服务名
     * @param apiPath                    实际请求path路径
     * @param apiMethod                  实际请求方式
     * @param gateway                    实际调用网关
     * @param appKey                     调用的appKey
     * @param authMode                   调用的鉴权模式
     * @param appAuthMethod              应用鉴权方式
     * @param authFailStatusEnum         鉴权失败状态
     */
    public ApiStatisticsEvent(Object source, String applicationName, String apiPath, String apiMethod, String gateway,
                              String appKey, String authMode, String appAuthMethod, AuthFailStatusEnum authFailStatusEnum) {
        super(source);
        this.applicationName = applicationName;
        this.apiPath = apiPath;
        this.apiMethod = apiMethod;
        this.gateway = gateway;
        this.appKey = appKey;
        this.authMode = authMode;
        this.appAuthMethod = appAuthMethod;
        this.authFailStatusEnum = authFailStatusEnum;
    }

    public String genKey() {
        return LocalDateUtils.localDateTime2String(LocalDateTime.now(), LocalDateUtils.Pattern.YYYYMMDDHH) + "0000" +
                applicationName + apiPath + apiMethod + appKey + authMode + appAuthMethod;
    }

    public String getPeriod(String key) {
        return key.substring(0, 14);
    }

}
