package com.yxt.safecenter.auth.sdk.utils;


import com.yxt.safecenter.auth.sdk.constants.CommonConstants;
import com.yxt.safecenter.auth.sdk.dto.JWTInfo;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.joda.time.DateTime;

public class JWTHelper {
    private static RsaKeyHelper rsaKeyHelper = new RsaKeyHelper();
    /**
     * 密钥加密token
     *
     * @param jwtInfo
     * @param priKeyPath
     * @param expire
     * @return
     * @throws Exception
     */
    public static String generateToken(JWTInfo jwtInfo, String priKeyPath, int expire) throws Exception {
        String compactJws = Jwts.builder()
                .setSubject(jwtInfo.getUserId())
                .claim(CommonConstants.JWT_KEY_NAME, jwtInfo.getUserName())
                .claim(CommonConstants.JWT_KEY_ZH_NAME, jwtInfo.getZhName())
                .setExpiration(DateTime.now().plusSeconds(expire).toDate())
                .signWith(SignatureAlgorithm.RS256, rsaKeyHelper.getPrivateKey(priKeyPath))
                .compact();
        return compactJws;
    }

    /**
     * 密钥加密token
     *
     * @param jwtInfo
     * @param priKey
     * @param expire
     * @return
     * @throws Exception
     */
    public static String generateToken(JWTInfo jwtInfo, byte priKey[], int expire) throws Exception {
        String compactJws = Jwts.builder()
                .setSubject(jwtInfo.getUserId())
                .claim(CommonConstants.JWT_KEY_NAME, jwtInfo.getUserName())
                .claim(CommonConstants.JWT_KEY_ZH_NAME, jwtInfo.getZhName())
                .setExpiration(DateTime.now().plusSeconds(expire).toDate())
                .signWith(SignatureAlgorithm.RS256, rsaKeyHelper.getPrivateKey(priKey))
                .compact();
        return compactJws;
    }

    /**
     * 公钥解析token
     *
     * @param token
     * @return
     * @throws Exception
     */
    public static Jws<Claims> parserToken(String token, String pubKeyPath) throws Exception {
        Jws<Claims> claimsJws = Jwts.parser().setSigningKey(rsaKeyHelper.getPublicKey(pubKeyPath)).parseClaimsJws(token);
        return claimsJws;
    }
    /**
     * 公钥解析token
     *
     * @param token
     * @return
     * @throws Exception
     */
    public static Jws<Claims> parserToken(String token, byte[] pubKey) throws Exception {
        Jws<Claims> claimsJws = Jwts.parser().setSigningKey(rsaKeyHelper.getPublicKey(pubKey)).parseClaimsJws(token);
        return claimsJws;
    }
    /**
     * 获取token中的用户信息
     *
     * @param token
     * @param pubKeyPath
     * @return
     * @throws Exception
     */
    public static JWTInfo getInfoFromToken(String token, String pubKeyPath) throws Exception {
        Jws<Claims> claimsJws = parserToken(token, pubKeyPath);
        Claims body = claimsJws.getBody();
        Object accTypeObj = body.get(CommonConstants.JWT_KEY_ACC_TYPE);
        return new JWTInfo(body.getSubject(), StringHelper.getObjectValue(body.get(CommonConstants.JWT_KEY_NAME)),
                StringHelper.getObjectValue(body.get(CommonConstants.JWT_KEY_ZH_NAME)), StringHelper.getObjectValue(body.get(CommonConstants.JWT_KEY_MERCODE)),
                StringHelper.getObjectValue(body.get(CommonConstants.JWT_KEY_EMP_CODE)), accTypeObj == null ? null : Integer.valueOf(accTypeObj.toString()),
                StringHelper.getObjectValue(body.get(CommonConstants.JWT_KEY_MULTI_LOGIN)));
    }
    /**
     * 获取token中的用户信息
     *
     * @param token
     * @param pubKey
     * @return
     * @throws Exception
     */
    public static JWTInfo getInfoFromToken(String token, byte[] pubKey) throws Exception {
        Jws<Claims> claimsJws = parserToken(token, pubKey);
        Claims body = claimsJws.getBody();
        Object accTypeObj = body.get(CommonConstants.JWT_KEY_ACC_TYPE);
        return new JWTInfo(body.getSubject(), StringHelper.getObjectValue(body.get(CommonConstants.JWT_KEY_NAME))
                ,StringHelper.getObjectValue(body.get(CommonConstants.JWT_KEY_ZH_NAME)),StringHelper.getObjectValue(body.get(CommonConstants.JWT_KEY_MERCODE)),
                StringHelper.getObjectValue(body.get(CommonConstants.JWT_KEY_EMP_CODE)), accTypeObj == null ? null : Integer.valueOf(accTypeObj.toString()),
                StringHelper.getObjectValue(body.get(CommonConstants.JWT_KEY_MULTI_LOGIN)));
    }
}
