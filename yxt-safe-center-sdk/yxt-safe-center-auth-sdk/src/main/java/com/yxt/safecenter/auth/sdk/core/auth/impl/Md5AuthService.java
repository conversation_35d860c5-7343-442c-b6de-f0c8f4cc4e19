package com.yxt.safecenter.auth.sdk.core.auth.impl;

import com.yxt.lang.util.MD5Util;
import com.yxt.safecenter.auth.sdk.config.SafeCenterUserAuthConfig;
import com.yxt.safecenter.auth.sdk.constants.AuthConstants;
import com.yxt.safecenter.auth.sdk.dto.AppConfigInterfaceDTO;
import com.yxt.safecenter.auth.sdk.enums.AuthFailStatusEnum;
import com.yxt.safecenter.auth.sdk.service.SafeCenterDataService;
import com.yxt.safecenter.auth.sdk.utils.HttpUtils;
import com.yxt.safecenter.auth.sdk.utils.ResultVO;
import com.yxt.safecenter.auth.sdk.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.Set;

/**
 * api网关中：AuthGatewayFilterFactory
 * Since: 2025/1/14 15:36
 * Author: qs
 */

@Slf4j
@Service("APP_MD5")
public class Md5AuthService extends AbstractAuthService {

    private static final String HEADER_APPKEY = "appkey";
    private static final String HEADER_TIMESTAMP = "timestamp";
    private static final String HEADER_SIGN = "sign";

    @Autowired
    private SafeCenterUserAuthConfig safeCenterUserAuthConfig;

    @Resource
    private SafeCenterDataService safeCenterDataService;

    @Override
    protected Boolean isInterceptAppNotAuthReq() {
        return Boolean.TRUE;
    }

    @Override
    public Mono<Void> auth(ServerWebExchange exchange, GatewayFilterChain chain) {
        if (!safeCenterUserAuthConfig.isAuth()) {
            return chain.filter(exchange);
        }

        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();

        String urlPath = request.getURI().getPath();

        // 白名单接口
        String[] ignoredPatterns = safeCenterUserAuthConfig.getIgnoredPatterns().split("\\,");
        if (ignoredPatterns.length > 0) {
            AntPathMatcher antPathMatcher = new AntPathMatcher();
            for (String pattern : ignoredPatterns) {
                if (antPathMatcher.match(pattern, urlPath)) {
                    return chain.filter(exchange);
                }
            }
        }

        // ip 验证
        String[] urlPaths = StringUtils.tokenizeToStringArray(urlPath, "/");
        SafeCenterUserAuthConfig.WhiteList whiteList = safeCenterUserAuthConfig.getWhiteList();
        Set<String> ipIgnoreds = whiteList.getSystemMapIps().get(urlPaths[0]);
        if (!CollectionUtils.isEmpty(ipIgnoreds) && !ipIgnoreds.contains(getClientIp(request))) {
            response.setStatusCode(HttpStatus.UNAUTHORIZED);
            DataBuffer dataBuffer = response.bufferFactory().
                    wrap(StringUtil.convert2Bytes(ResultVO.fail(HttpStatus.UNAUTHORIZED, "ip受限，请联系平台运营人员配置ip百名单")));
            response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
            exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.AUTH_FAIL);
            return response.writeWith(Flux.just(dataBuffer));
        }

        // 认证信息参数效验
        Optional<String> appKeyOptional = request.getHeaders().getValuesAsList(HEADER_APPKEY).stream().findFirst();
        Optional<String> timestampOptional = request.getHeaders().getValuesAsList(HEADER_TIMESTAMP).stream().findFirst();
        Optional<String> signOptional = request.getHeaders().getValuesAsList(HEADER_SIGN).stream().findFirst();

        if (!appKeyOptional.isPresent()
                || !timestampOptional.isPresent()
                || !signOptional.isPresent()) {
            response.setStatusCode(HttpStatus.BAD_REQUEST);
            DataBuffer dataBuffer = response.bufferFactory().
                    wrap(StringUtil.convert2Bytes(ResultVO.fail(HttpStatus.UNAUTHORIZED, "appKey,timestamp,sign_method,sign不能为空。")));
            response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
            exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.AUTH_FAIL);
            return response.writeWith(Flux.just(dataBuffer));
        }

        // appKey信息效验 简单签名方式
        AppConfigInterfaceDTO appConfig = safeCenterDataService.getAppConfig(appKeyOptional.get());
        if (appConfig == null) {
            response.setStatusCode(HttpStatus.UNAUTHORIZED);
            DataBuffer dataBuffer = response.bufferFactory().
                    wrap(StringUtil.convert2Bytes(ResultVO.fail(HttpStatus.UNAUTHORIZED, "根据appkey[" + appKeyOptional.get() + "]找不到机构数据。")));
            response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
            exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.AUTH_FAIL);
            return response.writeWith(Flux.just(dataBuffer));
        }

        // 验证签名
        String signedString = MD5Util.MD5Encode(appConfig.getAppKey() + appConfig.getPublicKey() + timestampOptional.get(), StandardCharsets.UTF_8.name()).toLowerCase();
        if (Long.parseLong(timestampOptional.get()) + 3 * 60 * 1000 < System.currentTimeMillis()) {
            response.setStatusCode(HttpStatus.UNAUTHORIZED);
            DataBuffer dataBuffer = response.bufferFactory().
                    wrap(StringUtil.convert2Bytes(ResultVO.fail(HttpStatus.UNAUTHORIZED, "签名超时。")));
            response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
            exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.AUTH_FAIL);
            return response.writeWith(Flux.just(dataBuffer));
        }
        if (!signOptional.get().equalsIgnoreCase(signedString)) {
            response.setStatusCode(HttpStatus.UNAUTHORIZED);
            DataBuffer dataBuffer = response.bufferFactory().
                    wrap(StringUtil.convert2Bytes(ResultVO.fail(HttpStatus.UNAUTHORIZED, "验证签名失败。")));
            response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
            exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.AUTH_FAIL);
            return response.writeWith(Flux.just(dataBuffer));
        }
        return chain.filter(exchange);
    }

    protected String getClientIp(ServerHttpRequest request) {
        String clientIp = request.getHeaders().getFirst("X-CLIENT-IP");
        if (!StringUtils.isEmpty(clientIp)) {
            InetSocketAddress remoteAddress = request.getRemoteAddress();
            clientIp = remoteAddress != null ? remoteAddress.getAddress().getHostAddress() : null;
        }
        return clientIp;
    }
}
