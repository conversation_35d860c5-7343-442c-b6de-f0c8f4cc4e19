package com.yxt.safecenter.auth.sdk.core.auth.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.yxt.lang.exception.YxtBaseException;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.auth.sdk.constants.AuthConstants;
import com.yxt.safecenter.auth.sdk.constants.Const;
import com.yxt.safecenter.auth.sdk.dto.MemberInfoDTO;
import com.yxt.safecenter.auth.sdk.enums.AuthFailStatusEnum;
import com.yxt.safecenter.auth.sdk.enums.MemberStatusEnum;
import com.yxt.safecenter.auth.sdk.enums.PlatformEnum;
import com.yxt.safecenter.auth.sdk.service.feign.*;
import com.yxt.safecenter.auth.sdk.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.yxt.safecenter.auth.sdk.constants.Const.*;

/**
 * Since: 2025/1/21 18:27
 * Author: qs
 */

@Slf4j
@Service("SESSION_C")
public class YxdjLoginAuthService extends YxdjAbstractAuthService {

    private final String FLAG_URL = "/screenAdvert/receiveRewards";

    @Value("${auth-interceptor.ydj-login-url:http://mall.hxyxt.com/h5/pages/login/index?merCode=}")
    private String ydjLoginUrl;

    @Value("${auth-interceptor.charset:UTF-8}")
    private String charset;

    @Resource
    private CustomerFeignMonoService customerFeignMonoService;
    @Resource
    private MemberFeignMonoService memberFeignMonoService;
    @Resource
    private ScreenAdvertFeignMonoService screenAdvertFeignMonoService;

    @Override
    protected Mono<Void> auth(ServerWebExchange exchange, GatewayFilterChain chain) {
        try {
            ServerHttpRequest request = exchange.getRequest();
            ServerHttpRequest.Builder mutate = request.mutate();

            // 设置请求头不鉴权，网关处理过之后customer服务不处理
            mutate.headers(httpHeaders -> {
                httpHeaders.set(AuthConstants.OLD_AUTH_FLAG, Boolean.TRUE.toString());
            });
            PlatformEnum from = getUserAgent(request);
            Optional<String> tokenKey;
            log.debug("referer:{}, 渠道：{}", request.getHeaders().getFirst("referer"), from.name());
            if (Const.MINI_PROGRAM.equals(from.name())) {
                tokenKey = getTokenFromRequestToWeapp(request, USER_KEY);
                mutate.headers(httpHeaders -> {
                    httpHeaders.set(Const.HEAD_USER_ACCESS_CHANNEL, Const.MINI_PROGRAM);
                });
            } else {
                tokenKey = getTokenFromRequest(request, USER_KEY);
            }

            String merCode = getMerCodeFromRequest(request);
            if (StringUtils.isEmpty(merCode)) {
                return response(exchange.getResponse(), "50000", "前方拥堵，请稍等片刻~");
            }

            log.debug("--------------->login auth begin<---------------");
            if (tokenKey.isPresent()) {
                log.debug("tokenKey=>{}, 平台=>{}", tokenKey.get(), from.getCode());
                return memberFeignMonoService.checkToken(merCode, tokenKey.get(), true).flatMap(loginResponse -> {
                    log.debug("MemberService login response code {} {} {}", loginResponse.getCode(), loginResponse.getMsg(), loginResponse.getData());
                    MemberInfoDTO memberInfoDTO;
                    if (loginResponse.checkSuccess() && (memberInfoDTO = loginResponse.getData()) != null) {
                        return sucRespHand(exchange, chain, memberInfoDTO, merCode, request, from, tokenKey, mutate);
                    } else {
                        exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.AUTH_FAIL);
                        return buildNotLoginRespFailInfo(exchange, request, merCode, from);
                    }
                }).onErrorResume(err -> {
                    ExLogger.logger().error("[CHECK TOKEN] login response error", err);
                    exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.AUTH_FAIL);
                    return response(exchange.getResponse(), "50000", "前方拥堵，请稍等片刻~");
                });
            } else {
                log.debug("tokenKey=>为空");
                return buildNotLoginRespFailInfo(exchange, request, merCode, from);
            }
        } catch (YxtBaseException e) {
            return response(exchange.getResponse(), e.getCode(), e.getMessage());
        } catch (Exception e) {
            // TODO 编码
            return response(exchange.getResponse(), "50000", "前方拥堵，请稍等片刻~");
        }
    }

    private Mono<Void> sucRespHand(ServerWebExchange exchange, GatewayFilterChain chain, MemberInfoDTO memberInfoDTO, String merCode, ServerHttpRequest request, PlatformEnum from, Optional<String> finalTokenKey, ServerHttpRequest.Builder mutate) {
        log.info("todo delete memberInfoDTO.getMemberStatus:{}", memberInfoDTO.getMemberStatus());
        if (MemberStatusEnum.FROZEN.getCode().equals(memberInfoDTO.getMemberStatus())) {
            log.info("todo delete 即将返回false 700019");
            exchange.getAttributes().put(AuthConstants.AUTH_FAIL_STATUS, AuthFailStatusEnum.AUTH_FAIL);
            return response(exchange.getResponse(), "700019", "该账户已冻结,请联系客服 400-9889-889");
        }
        log.debug("todo delete 没有进入冻结");
        do {
            if (!StringUtils.equalsIgnoreCase(merCode, memberInfoDTO.getMerCode())) {
                log.info("[New-AuthInterceptor] MerCode is not match, requestMerCode={} cacheMerCode={}",
                        merCode, memberInfoDTO.getMerCode());
                return buildNotLoginRespFailInfo(exchange, request, merCode, from);
            }

            String appId = getAppId(merCode, from, request);
            String openId = memberInfoDTO.getOpenIdBy(from.name(), appId);
            if (Objects.isNull(openId)) {
                log.info("[New-AuthInterceptor] User is not login on this {} platform token={}",
                        from.name(), finalTokenKey.get());
                return buildNotLoginRespFailInfo(exchange, request, merCode, from);
            }

            String userId = memberInfoDTO.getUserId().toString();
            mutate.headers(httpHeaders -> {
                httpHeaders.set(AuthConstants.OLD_AUTH_FLAG, Boolean.FALSE.toString());
                httpHeaders.set(USER_KEY, finalTokenKey.get());
                httpHeaders.set(Const.HEAD_USER_ID_KEY, userId);
                httpHeaders.set(Const.HEAD_OPEN_ID_KEY, openId);
                httpHeaders.set(Const.HEAD_UNION_ID_KEY, memberInfoDTO.getUnionIdBy(from, appId));
                httpHeaders.set(Const.HEAD_MEMBER_CARD_KEY, memberInfoDTO.getMemberCard());
                httpHeaders.set(Const.HEAD_EMPLOYEE_FLAG, memberInfoDTO.getEmpFlag());
            });
            log.debug("User auth passed and loginInfo is userId={} tokenKey={} openId={}",
                    userId, finalTokenKey.get(), openId);
            return chain.filter(exchange);
        } while (false);
    }

    /**
     * 构建未登录的响应信息
     * @param exchange
     * @param request
     * @param merCode
     * @param from
     * @return
     */
    private Mono<Void> buildNotLoginRespFailInfo(ServerWebExchange exchange, ServerHttpRequest request, String merCode, PlatformEnum from) {
        log.info("[New-AuthInterceptor] User need to login merCode={}", merCode);
        //弹广告设置标识
        String reqPath = HttpUtils.getGatewayStripPrefixPath(exchange.getRequest().getURI().getPath());
        Mono<Void> authResponseMono = Mono.defer(() -> {
            switch (from.name()) {
                case Const.WX_ACCOUNT:
                    return processFromWx(exchange, merCode);
                case Const.MINI_PROGRAM:
                    return processFromMiniProgram(exchange);
                case Const.H5:
                    return processFromH5(exchange, merCode);
                default:
                    log.warn("It's not going to be here ^_^");
                    return response(exchange.getResponse(), "50000", "前方拥堵，请稍等片刻~");
            }
        });

        if (reqPath.contains(FLAG_URL)) {
            // 从 Body 异步获取 Token
            return getBodyParams(request)
                    .flatMap(body -> {
                        String tokenStr = (String) body.get("tokenStr");
                        log.info("非登录标识 {}", tokenStr);
                        return screenAdvertFeignMonoService.setPopFlag(tokenStr, merCode)
                                // 确保在设置标识后继续鉴权响应
                                .then(authResponseMono);
                    })
                    .onErrorResume(e -> {
                        log.warn("处理弹窗标识失败，继续返回鉴权响应", e);
                        return authResponseMono;
                    });
        } else {
            return authResponseMono;
        }
    }

    /**
     * 获取body参数
     * 只能获取一次，只能在确定不进行接口转发才使用
     * @param request
     * @return
     */
    public static Mono<Map<String, Object>> getBodyParams(ServerHttpRequest request) {
        return DataBufferUtils.join(request.getBody())
                .map(dataBuffer -> {
                    byte[] bytes = new byte[dataBuffer.readableByteCount()];
                    dataBuffer.read(bytes);
                    DataBufferUtils.release(dataBuffer);
                    return new String(bytes, StandardCharsets.UTF_8);
                })
                .flatMap(body -> {
                    try {
                        if (StringUtils.isBlank(body)) {
                            return Mono.just(Collections.emptyMap());
                        }
                        Map<String, Object> parsedBody = JSON.parseObject(body, new TypeReference<Map<String, Object>>() {});
                        return Mono.just(parsedBody);
                    } catch (Exception e) {
                        log.warn("解析请求体失败，Body: {}", body, e);
                        return Mono.just(Collections.emptyMap()); // 解析失败返回空 Map
                    }
                });
    }

    private Mono<Void> processFromH5(ServerWebExchange exchange, String merCode) {
        StringBuilder redirect = new StringBuilder(ydjLoginUrl);
        redirect.append(merCode);
        String referer = marshallerFromUrlEncode(exchange.getRequest(), charset);
        if (StringUtils.isNotBlank(referer)) {
            redirect.append("&from_url=").append(referer);
        }

        Map<String, String> params = Maps.newLinkedHashMap();
        params.put("redirectUrl", redirect.toString());
        return response(exchange.getResponse(), "70007", "未登录,或登陆超时", params);
    }

    private Mono<Void> processFromMiniProgram(ServerWebExchange exchange) {
        return response(exchange.getResponse(), "70007", "未登录,或登陆超时");
    }

    private Mono<Void> processFromWx(ServerWebExchange exchange, String merCode) {
        ServerHttpRequest request = exchange.getRequest();
        String referer = marshallerFromUrl(request);

        return customerFeignMonoService.buildAuthorizationUrl(merCode, referer).flatMap(response -> {
            if (!response.checkSuccess()) {
                return response(exchange.getResponse(), response.getCode(), response.getMsg());
            }
            if (StringUtils.isBlank(response.getData())) {
                log.error("[New-AuthInterceptor] Invoke (hydee-middle-third)-(buildUserAuthUrl) return NULL code={} msg={}`",
                        response.getCode(), response.getMsg());
                return response(exchange.getResponse(), "70010", "微信授权地址为空,无法跳转");
            }

            Map<String, String> params = Maps.newLinkedHashMap();
            params.put("redirectUrl", response.getData());
            return response(exchange.getResponse(), "70007", "未登录,或登陆超时", params);
        }).onErrorResume(err -> response(exchange.getResponse(), "50000", "前方拥堵，请稍等片刻~"));
    }

    String marshallerFromUrlEncode(ServerHttpRequest request, String charset) {
        String referer = marshallerFromUrl(request);
        if (StringUtils.isEmpty(referer)) {
            return referer;
        } else {
            try {
                return URLEncoder.encode(referer, charset);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        }
    }

    String marshallerFromUrl(ServerHttpRequest request) {
        String referer = request.getHeaders().getFirst("referer");
        if (StringUtils.isNotBlank(referer)) {
            if (StringUtils.isNotBlank(request.getURI().getQuery())) {
                referer = referer + "&" + request.getURI().getQuery();
            }
            return referer;
        }
        return StringUtils.EMPTY;
    }


}
