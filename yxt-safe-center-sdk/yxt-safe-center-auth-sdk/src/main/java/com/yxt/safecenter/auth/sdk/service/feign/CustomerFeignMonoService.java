package com.yxt.safecenter.auth.sdk.service.feign;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.safecenter.auth.sdk.dto.MemberInfoDTO;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * Since: 2025/04/24 20:19
 * Author: qs
 */
@Service
public class CustomerFeignMonoService {


    @Resource
    private CustomerFeignClient customerFeignClient;
    @Resource
    private Scheduler feignScheduler;

    public Mono<ResponseBase<String>> buildAuthorizationUrl(String merCode, String referer) {
        return Mono.fromCallable(() -> customerFeignClient.buildAuthorizationUrl(merCode, referer))
                .timeout(Duration.ofSeconds(5))
                .subscribeOn(feignScheduler);
    }
}
