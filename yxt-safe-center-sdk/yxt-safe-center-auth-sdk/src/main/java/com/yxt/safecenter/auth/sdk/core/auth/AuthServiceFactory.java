package com.yxt.safecenter.auth.sdk.core.auth;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Since: 2025/1/10 15:47
 * Author: qs
 */

@Component
public class AuthServiceFactory {

    private Map<String, AuthService> authStrategyMap;

    @Lazy
    @Autowired
    public void setAuthStrategyMap(Map<String, AuthService> authStrategyMap) {
        this.authStrategyMap = authStrategyMap;
    }

    public AuthService getStrategy(String strategyName) {
        return authStrategyMap.get(strategyName);
    }

}
