package com.yxt.safecenter.auth.sdk.core.filter;

import com.alibaba.fastjson.JSONArray;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.auth.sdk.config.GatewayExcludeUrlConfig;
import com.yxt.safecenter.auth.sdk.constants.AuthConstants;
import com.yxt.safecenter.auth.sdk.constants.RestCodeConstants;
import com.yxt.safecenter.auth.sdk.dto.AppConfigInterfaceDTO;
import com.yxt.safecenter.auth.sdk.dto.InterfaceInfoDTO;
import com.yxt.safecenter.auth.sdk.dto.resp.ReturnData;
import com.yxt.safecenter.auth.sdk.enums.AuthFailStatusEnum;
import com.yxt.safecenter.auth.sdk.event.ApiStatisticsEvent;
import com.yxt.safecenter.auth.sdk.core.auth.AuthServiceFactory;
import com.yxt.safecenter.auth.sdk.service.SafeCenterDataService;
import com.yxt.safecenter.auth.sdk.core.auth.AuthService;
import com.yxt.safecenter.auth.sdk.utils.HttpUtils;
import com.yxt.safecenter.auth.sdk.utils.InterfaceMatcherUtil;
import com.yxt.safecenter.auth.sdk.utils.StringUtil;
import com.yxt.safecenter.common.model.dto.AuthInfoDTO;
import com.yxt.safecenter.common.model.enums.AuthGatewayEnum;
import com.yxt.safecenter.common.model.enums.AuthModeEnum;
import com.yxt.safecenter.common.model.enums.InterfaceStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Since: 2025/1/3 16:40
 * Author: qs
 */

@Slf4j
@Component
public class SafeCenterAuthGatewayFilterFactory extends AbstractGatewayFilterFactory<SafeCenterAuthGatewayFilterFactory.Config> {

    public SafeCenterAuthGatewayFilterFactory() {
        super(Config.class);
    }

    @Value("${safe-center.gateway-channel: }")
    private String gatewayChannel;

    // 网关过滤器与鉴权方式映射关系
    @Value("#{${safe-center.filter-auth-mapping:{}}}")
    private Map<String, String> filterAuthMapping;

    @Value("${safe-center.auth.enable.all:false}")
    private Boolean authEnableAll;

    @Value("${safe-center.auth.enable.list:yxt-safe-center}")
    private List<String> authEnableList;

    @Value("${safe-center.auth.log.enable:false}")
    private Boolean logEnable;

    @Resource
    private GatewayExcludeUrlConfig gatewayExcludeUrlConfig;

    // 默认映射关系
    private static final Map<String, String> DEFAULT_AUTH_MAPPING = new HashMap<>();


    @Resource
    private AuthServiceFactory authServiceFactory;
    @Resource
    @Lazy
    private SafeCenterDataService safeCenterDataService;
    @Resource
    private ApplicationEventPublisher eventPublisher;

    static {
        // 设置默认映射关系
        DEFAULT_AUTH_MAPPING.put("AppKey", "RSA_AUTH");
        DEFAULT_AUTH_MAPPING.put("CheckSign", "RSA_AUTH_OLD");
        DEFAULT_AUTH_MAPPING.put("Auth", "MD5");
        DEFAULT_AUTH_MAPPING.put("Sign", "RSA_AUTH_V2");
    }

    @Override
    public GatewayFilter apply(Config config) {
        return new OrderedGatewayFilter((exchange, chain) -> {
            try {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                // 默认老的过滤器要鉴权
                exchange.getAttributes().put(AuthConstants.OLD_AUTH_FLAG, Boolean.TRUE);
                ServerHttpRequest request = exchange.getRequest();
                String requestUri = request.getPath().pathWithinApplication().value();
                String method = request.getMethodValue();
                String reqPath = HttpUtils.getGatewayStripPrefixPath(request.getURI().getPath());

                ServerHttpResponse response;

                Route route = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
                Assert.isTrue(Objects.nonNull(route), "route can not null");

                // 日志记录
                if (log.isDebugEnabled()) {
                    log.debug("method:{} requestUri：{}", method, requestUri);
                }

                // 获取接口信息
                String applicationName = route.getUri().getHost();
                List<InterfaceInfoDTO> interfaceInfoList = safeCenterDataService.getInterfaceList(applicationName);
                // 匹配接口
                InterfaceInfoDTO matchInterfaceInfoDTO = InterfaceMatcherUtil.matchApi(interfaceInfoList, reqPath, method);
                if (matchInterfaceInfoDTO == null && enableAuth(applicationName)) {
                    response = exchange.getResponse();
                    return getFailResponseMono(response, HttpStatus.NOT_FOUND, RestCodeConstants.URL_NOT_MATCH_CODE, HttpStatus.NOT_FOUND.name());
                }
                if (matchInterfaceInfoDTO == null && !enableAuth(applicationName)) {
                    return chain.filter(exchange);
                }
                assert matchInterfaceInfoDTO != null;
                if (!InterfaceStatusEnum.ONLINE.equals(InterfaceStatusEnum.getByName(matchInterfaceInfoDTO.getStatus())) && enableAuth(applicationName)) {
                    response = exchange.getResponse();
                    return getFailResponseMono(response, HttpStatus.FORBIDDEN, RestCodeConstants.URL_FORBIDDEN_NOT_ONLINE_CODE, "无接口权限：未发布接口");
                }
                AuthGatewayEnum gatewayEnum = AuthGatewayEnum.getByName(gatewayChannel);
                if (StringUtils.isEmpty(gatewayChannel) || gatewayEnum == null) {
                    // 未配置不做鉴权
                    ExLogger.logger().error("未配置网关名称或配置错误：{}", gatewayChannel);
                    return chain.filter(exchange);
                }
                String authMode = "";
                String authInfo = matchInterfaceInfoDTO.getAuthInfo();
                if (StringUtils.isNotEmpty(authInfo)) {
                    List<AuthInfoDTO> authInfoDTOList = JSONArray.parseArray(authInfo, AuthInfoDTO.class);
                    // 拦截非本网关接口
                    Optional<AuthInfoDTO> optionalAuthInfoDTO = authInfoDTOList.stream().filter(authInfoDTO -> authInfoDTO.getAuthGateway().equals(gatewayEnum)).findFirst();
                    if (optionalAuthInfoDTO.isPresent()) {
                        authMode = optionalAuthInfoDTO.get().getAuthMode().getCode();
                    }
                }

                if (StringUtils.isEmpty(authMode)) {
                    // 老接口获取鉴权模式，完全启用后不走该逻辑，为空直接失败
                    if (enableAuth(applicationName)) {
                        response = exchange.getResponse();
                        return getFailResponseMono(response, HttpStatus.FORBIDDEN, RestCodeConstants.URL_FORBIDDEN_NOT_CONFIG_CODE, "无接口权限：未配置网关鉴权模式");
                    }
                    authMode = getAuthMode(authMode, requestUri);
                    if (StringUtils.isEmpty(authMode)) {
                        return chain.filter(exchange);
                    }
                }

                String authMethod = "";
                String appKey = "";
                Mono<Void> mono = null;
                if (AuthModeEnum.NO.getName().equals(authMode)) {
                    exchange.getAttributes().put(AuthConstants.OLD_AUTH_FLAG, Boolean.FALSE);
                } else {
                    String beanName = authMode;
                    if (AuthModeEnum.APP.getName().equals(authMode)) {
                        // 获取应用
                        HttpHeaders headers = exchange.getRequest().getHeaders();
                        appKey = headers.getFirst(AuthConstants.SIGN_APP_KEY_PARAM_NAME);
                        // 没有则取hydee-api-merCode的值
                        if (StringUtils.isEmpty(appKey)) {
                            appKey = headers.getFirst(AuthConstants.SIGN_HEADER_MER_CODE_PARAM_NAME);
                        }
                        if (StringUtils.isEmpty(appKey)) {
                            response = exchange.getResponse();
                            return getFailResponseMono(response, HttpStatus.BAD_REQUEST, RestCodeConstants.BAD_REQUEST_NOT_APP_KEY_CODE, "appKey不能为空。");
                        }

                        // 获取应用
                        AppConfigInterfaceDTO appConfigInterfaceDTO = safeCenterDataService.getAppConfig(appKey);
                        authMethod = appConfigInterfaceDTO.getAuthMethod();
                        if (StringUtils.isEmpty(authMethod)) {
                            // 完全上线后不走该逻辑，直接失败
                            if (enableAuth(applicationName)) {
                                response = exchange.getResponse();
                                return getFailResponseMono(response, HttpStatus.FORBIDDEN, RestCodeConstants.URL_FORBIDDEN_NOT_CONFIG_CODE, "请维护应用鉴权方式");
                            }
                            authMethod = getAuthMethod(route, authMethod);
                            if (StringUtils.isEmpty(authMethod)) {
                                return chain.filter(exchange);
                            }
                        }

                        beanName = authMode + "_" + authMethod;
                    }

                    // 执行鉴权
                    if (enableAuth(applicationName)) {
                        // 执行新鉴权，老的过滤器不鉴权
                        exchange.getAttributes().put(AuthConstants.OLD_AUTH_FLAG, Boolean.FALSE);
                        AuthService authService = authServiceFactory.getStrategy(beanName);
                        mono = authService.execute(exchange, chain, matchInterfaceInfoDTO, appKey);
                    }
                }

                // 发布统计事件 统一在此处处理有一个问题，鉴权中有异步feign调用的逻辑则无法获取到鉴权结果
                try {
                    AuthFailStatusEnum authFailStatusEnum = (AuthFailStatusEnum) exchange.getAttributes().get(AuthConstants.AUTH_FAIL_STATUS);
                    ApiStatisticsEvent event = new ApiStatisticsEvent(this, applicationName, reqPath, method, gatewayChannel, appKey,
                            authMode, authMethod, authFailStatusEnum);
                    eventPublisher.publishEvent(event);
                } catch (Exception e) {
                    ExLogger.logger("SafeCenterAuth").error("接口统计事件异常", e);
                }

                if (mono == null) {
                    return chain.filter(exchange.mutate().request(request).build());
                }
                stopWatch.stop();
                if (logEnable) {
                    ExLogger.logger("safeCenterAuth").info("接口安全中心拦截器非鉴权逻辑耗时：{}ms", stopWatch.getTime());
                }
                return mono;
            } catch (Exception e) {
                ExLogger.logger("safe-center-auth").error("鉴权异常", e);
                // 使用老鉴权过滤器鉴权
                exchange.getAttributes().put(AuthConstants.OLD_AUTH_FLAG, Boolean.TRUE);
                return chain.filter(exchange);
            }
            // 最早的鉴权顺序，配置时放在YxtLoginAccessGatewayFilter前面
        }, FilterOrder.YxtLoginAccessGatewayFilter);
    }

    /**
     * 是否启用新鉴权
     *
     * @param applicationName 服务名
     * @return true/false
     */
    private boolean enableAuth(String applicationName) {
        return authEnableAll || authEnableList.contains(applicationName);
    }

    private String getAuthMethod(Route route, String authMethod) {
        // 获取路由中的过滤器，从过滤器中取具体的鉴权方式
        List<String> filterNames = route.getFilters().stream()
                .map(this::getClassName)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterAuthMapping)) {
            filterAuthMapping = DEFAULT_AUTH_MAPPING;
        }
        String mappingKey = "";
        Set<String> mappingKeySet = filterAuthMapping.keySet();
        for (String filterName : filterNames) {
            for (String key : mappingKeySet) {
                if (filterName.startsWith(key)) {
                    mappingKey = key;
                    break;
                }
            }
            if (StringUtils.isNotEmpty(mappingKey)) {
                break;
            }
        }
        if (StringUtils.isNotEmpty(mappingKey)) {
            authMethod = filterAuthMapping.get(mappingKey);
        } else {
            // TODO 不鉴权
            ExLogger.logger().warn("过滤器映射关系未匹配：过滤器：{}，映射关系：{}", filterNames, filterAuthMapping);
        }
        return authMethod;
    }

    private String getClassName(GatewayFilter filter) {
        String className = "";
        if (filter instanceof OrderedGatewayFilter) {
            GatewayFilter delegate = ((OrderedGatewayFilter) filter).getDelegate();
            className = delegate.getClass().getSimpleName();
        }
        return StringUtils.isEmpty(className) ? filter.getClass().getSimpleName() : className;
    }

    /**
     * 老接口获取鉴权模式
     */
    private String getAuthMode(String authMode, String oriReqPath) {
        AuthGatewayEnum gatewayEnum = AuthGatewayEnum.getByName(gatewayChannel);
        if (StringUtils.isEmpty(gatewayChannel) || gatewayEnum == null) {
            // 未配置不做网关匹配鉴权
            ExLogger.logger().error("未配置网关名称或配置错误：{}", gatewayChannel);
            return authMode;
        }

        switch (gatewayEnum) {
            case API:
                authMode = AuthModeEnum.APP.getName();
                break;
            case B:
            case BIGDATA:
                // 都需要SESSION鉴权，不需要鉴权的根据配置
                if (gatewayExcludeUrlConfig.isStartWith(oriReqPath) || gatewayExcludeUrlConfig.isEndWith(oriReqPath)) {
                    authMode = AuthModeEnum.NO.getName();
                } else {
                    authMode = AuthModeEnum.SESSION_B.getName();
                }
                break;
            case C:
                // C端网关的接口需要根据配置提前维护鉴权模式
                ExLogger.logger().error("C端接口需要维护鉴权模式：{}", oriReqPath);
        }
        return authMode;
    }

    private static Mono<Void> getFailResponseMono(ServerHttpResponse response, HttpStatus httpStatus, Integer code, String errors) {
        response.setStatusCode(httpStatus);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        // 直接在当前线程中序列化并写入响应体
        return Mono.fromCallable(() -> {
                    byte[] bytes = StringUtil.convert2Bytes(
                            new ReturnData<>(code, errors));
                    return response.bufferFactory().wrap(bytes);
                })
                .flatMap(dataBuffer -> response.writeWith(Mono.just(dataBuffer)))
                .onErrorResume(Mono::error);
    }

    public static class Config {
        // 配置类，可以添加自定义配置属性，后续拓展可能会用到
    }


}
