package com.yxt.safecenter.auth.sdk.utils;

import org.springframework.http.HttpStatus;

import java.io.Serializable;

/**
 * Since: 2025/1/14 11:57
 * Author: qs
 */
// TODO 考虑统一返回对象使用问题
public class ResultVO<T> implements Serializable {

    protected boolean result = false;
    protected String count = "0";
    protected String status;
    protected String errors;
    protected T data;

    public ResultVO() {
    }

    public ResultVO(boolean result, String status, T data) {
        this.result = result;
        this.status = status;
        this.data = data;
    }

    public ResultVO(boolean result, String status, String errors) {
        this.result = result;
        this.status = status;
        this.errors = errors;
    }


    public static <T> ResultVO<T> success(T data) {
        return new ResultVO<T>(true, "200", data);
    }

    public static <T> ResultVO<T> fail() {
        return new ResultVO<T>();
    }

    public static <T> ResultVO<T> fail(String status, String errors) {
        return new ResultVO<T>(false, status, errors);
    }

    public static <T> ResultVO<T> fail(HttpStatus status, String errors) {
        return new ResultVO<T>(false, String.valueOf(status.value()), errors);
    }

    public boolean isResult() {
        return this.result;
    }

    public String getCount() {
        return this.count;
    }

    public String getStatus() {
        return this.status;
    }

    public String getErrors() {
        return this.errors;
    }

    public T getData() {
        return this.data;
    }

    public void setResult(boolean result) {
        this.result = result;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setErrors(String errors) {
        this.errors = errors;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String toString() {
        return "BaseVO(result=" + this.isResult() + ", count=" + this.getCount() + ", status=" + this.getStatus() + ", errors=" + this.getErrors() + ", data=" + this.getData() + ")";
    }

}
