package com.yxt.safecenter.auth.sdk.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Since: 2025/2/14 13:49
 * Author: qs
 */
public class LocalDateUtils {

    /**
     * 将LocalDateTime转为String yyyy-MM-dd HH:mm:ss 格式
     * @param localDateTime 时间
     * @return String
     */
    public static String localDateTime2String(LocalDateTime localDateTime) {
        return localDateTime2String(localDateTime, Pattern.YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 将LocalDateTime转为String
     * @param localDateTime 时间
     * @param pattern 时间格式
     * @return String
     */
    public static String localDateTime2String(LocalDateTime localDateTime, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(formatter);
    }

    /**
     * String 转换 LocalDateTime
     * @param dateTimeStr 时间
     * @param pattern 时间格式
     * @return LocalDateTime
     */
    public static LocalDateTime string2LocalDateTime(String dateTimeStr, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.parse(dateTimeStr, formatter);
    }

    // pattern类
    public static class Pattern {
        public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
        public static final String YYYY_MM_DD = "yyyy-MM-dd";
        public static final String YYYYMMDD = "yyyyMMdd";
        public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
        public static final String YYYYMMDDHH = "yyyyMMddHH";
    }
}
