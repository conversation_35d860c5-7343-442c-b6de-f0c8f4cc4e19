package com.yxt.safecenter.auth.sdk.utils;

import com.yxt.safecenter.auth.sdk.config.SafeCenterKeyConfiguration;
import com.yxt.safecenter.auth.sdk.dto.JWTInfo;
import com.yxt.safecenter.auth.sdk.dto.exception.UserTokenException;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.SignatureException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SafeCenterUserAuthUtil {

    @Autowired
    private SafeCenterKeyConfiguration safeCenterKeyConfiguration;

    public JWTInfo getInfoFromToken(String token) throws Exception {
        try {
            return JWTHelper.getInfoFromToken(token, safeCenterKeyConfiguration.getUserPubKey());
        }catch (ExpiredJwtException ex){
            throw new UserTokenException("User token expired!");
        }catch (SignatureException ex){
            throw new UserTokenException("User token signature error!");
        }catch (IllegalArgumentException ex){
            throw new UserTokenException("User token is null or empty!");
        }
    }
}
