package com.yxt.safecenter.auth.sdk.dto.statistics;

import com.yxt.safecenter.auth.sdk.utils.LocalDateUtils;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Since: 2025/2/13 11:39
 * Author: qs
 */

public class ApiStatisticsDTO {

    /**
     * 服务名
     */
    @Getter
    private String applicationName;

    /**
     * 实际请求path路径，只有一个值
     */
    @Getter
    private String apiPath;

    /**
     * 实际请求方式，只有一个值
     */
    @Getter
    private String apiMethod;

    /**
     * 实际调用网关 C-小程序网关 API-对外网关  BIGDATA-大数据网关 BUS-B端用户网关
     */
    @Getter
    private String gateway;

    /**
     * 调用的appKey
     */
    @Getter
    private String appKey;

    /**
     * 调用的鉴权模式 SESSION-会话 SIGN-验签 为空不鉴权
     */
    @Getter
    private String authMode;

    /**
     * 应用授权方式
     */
    @Getter
    private String appAuthMethod;

    /**
     * 统计周期
     */
    @Getter
    private String period;

    /**
     * 统计开始时间
     */
    @Getter
    private LocalDateTime startTime;

    /**
     * 统计结束时间
     */
    @Getter
    private LocalDateTime endTime;

    public ApiStatisticsDTO(String applicationName, String apiPath, String apiMethod,
                            String gateway, String appKey, String authMode, String appAuthMethod, String period) {
        this.applicationName = applicationName;
        this.apiPath = apiPath;
        this.apiMethod = apiMethod;
        this.gateway = gateway;
        this.appKey = appKey;
        this.authMode = authMode;
        this.appAuthMethod = appAuthMethod;
        this.period = period;
        this.startTime = LocalDateUtils.string2LocalDateTime(period, LocalDateUtils.Pattern.YYYYMMDDHHMMSS);
        this.endTime = this.startTime.plusHours(1);
    }

    /**
     * 总调用次数
     */
    private final AtomicInteger totalCalls = new AtomicInteger(0);
    /**
     * 调用渠道不匹配拦截次数
     */
    private final AtomicInteger channelInterceptions = new AtomicInteger(0);
    /**
     * rbac鉴权拦截次数
     */
    private final AtomicInteger rbacInterceptions = new AtomicInteger(0);
    /**
     * 认证鉴权拦截次数
     */
    private final AtomicInteger authInterceptions = new AtomicInteger(0);

    public void incrementTotalCalls() {
        totalCalls.incrementAndGet();
    }

    public void incrementChannelInterceptions() {
        channelInterceptions.incrementAndGet();
    }

    public void incrementRbacInterceptions() {
        rbacInterceptions.incrementAndGet();
    }

    public void incrementAuthInterceptions() {
        authInterceptions.incrementAndGet();
    }


    public Integer getTotalCalls() {
        return totalCalls.get();
    }

    public Integer getChannelInterceptions() {
        return channelInterceptions.get();
    }

    public Integer getRbacInterceptions() {
        return rbacInterceptions.get();
    }

    public Integer getAuthInterceptions() {
        return authInterceptions.get();
    }

}

