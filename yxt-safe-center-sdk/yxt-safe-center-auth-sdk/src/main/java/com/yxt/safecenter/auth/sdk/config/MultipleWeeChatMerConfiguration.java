package com.yxt.safecenter.auth.sdk.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import java.util.Arrays;
import java.util.Optional;

/**
 * 多小程序商户配置
 * Since: 2025/1/23 10:32
 * Author: qs
 */

@Slf4j
@Configuration
@ConfigurationProperties(prefix = "multiple-wechat")
@Data
public class MultipleWeeChatMerConfiguration {

    private String merCodes;

    /**
     * (现在/曾经)是否配置了多小程序的商户
     */
    public Boolean isMultipleAppMerCode(String merCode) {
        log.debug("是否配置了多小程序的商户, config:{}, merCode:{}", merCodes, merCode);
        if (StringUtils.isBlank(merCode) || StringUtils.isBlank(merCodes)) {
            return false;
        }
        Optional<String> first = Arrays.stream(merCodes.split(",")).filter(code -> StringUtils.equals(code, merCode)).findFirst();
        return first.isPresent();
    }
}
