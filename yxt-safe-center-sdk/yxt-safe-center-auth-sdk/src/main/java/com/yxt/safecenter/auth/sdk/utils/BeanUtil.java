package com.yxt.safecenter.auth.sdk.utils;

import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2022/5/15 23:59
 */
public class BeanUtil {

    public static <T, V> List<T> copyList(List<V> list, Class<T> clazz) {
        List<T> targetList = new ArrayList<T>();
        if (CollectionUtils.isEmpty(list)) {
            return targetList;
        }
        T target = null;
        for (V source : list) {
            try {
                target = clazz.newInstance();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            BeanUtils.copyProperties(source, target);
            targetList.add(target);
        }
        return targetList;
    }

    public static void copyProperties(Object source, Object target) {
        BeanUtils.copyProperties(source, target);
    }

    public static void copyProperties(Object source, Object target, Boolean skipNull) {
        if (skipNull && (source == null || target == null)) {
            return;
        }
        BeanUtils.copyProperties(source, target);
    }

    public static <T> T copyProperties(Object source, Class<T> target) {
        T t;
        try {
            t = target.newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (source != null) {
            BeanUtils.copyProperties(source, t);
        }
        return t;
    }

}
