package com.yxt.safecenter.auth.sdk.core.auth.impl;


import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lang.util.ExLogger;
import com.yxt.safecenter.auth.sdk.config.MultipleWeeChatMerConfiguration;
import com.yxt.safecenter.auth.sdk.constants.Const;
import com.yxt.safecenter.auth.sdk.enums.PlatformEnum;
import com.yxt.safecenter.auth.sdk.utils.HttpUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static com.yxt.safecenter.auth.sdk.constants.Const.MINI_USER_AGENT;
import static com.yxt.safecenter.auth.sdk.constants.Const.WX_USER_AGENT;
import static com.yxt.safecenter.auth.sdk.enums.PlatformEnum.H5;
import static com.yxt.safecenter.auth.sdk.enums.PlatformEnum.OFFIACCOUNT;

@Service
public abstract class YxdjAbstractAuthService extends AbstractAuthService {

    @Resource
    @Lazy
    protected MultipleWeeChatMerConfiguration multipleWeeChatMerConfiguration;

    @Override
    protected Boolean isInterceptAppNotAuthReq() {
        return Boolean.FALSE;
    }

    PlatformEnum getUserAgent(ServerHttpRequest request) {
        Optional<PlatformEnum> optional = getPlatformEnum(request);
        if (optional.isPresent()) return optional.get();
        ExLogger.logger().warn("Unknown platform  user-agent={} ", request.getHeaders().getFirst("User-Agent"));
        // customer的业务系统逻辑是抛异常，返回调用方为 http status 200 code 20001
        throw new YxtBizException("请求参数错误");
    }

    PlatformEnum getUserPlatform(ServerHttpRequest request) {
        Optional<PlatformEnum> optional = getPlatformEnum(request);
        return optional.orElse(null);
    }

    public static Optional<PlatformEnum> getPlatformEnum(ServerHttpRequest request) {
        if (request == null) {
            return Optional.empty();
        }
        String channelId = request.getHeaders().getFirst("channelId");
        if (StringUtils.equalsIgnoreCase(channelId, MINI_USER_AGENT)) {
            return Optional.of(PlatformEnum.WEAPP);
        }

        String userAgent = request.getHeaders().getFirst("User-Agent");
        return Optional.of(StringUtils.contains(userAgent, WX_USER_AGENT) ?
                OFFIACCOUNT : H5);
    }

    Optional<String> getTokenFromRequest(ServerHttpRequest request, String tokenName) {
        return Optional.ofNullable(
                ObjectUtils.firstNonNull(
                        HttpUtils.getCookieValueByName(request, tokenName),
                        request.getHeaders().getFirst(tokenName)
                ));
    }

    Optional<String> getTokenFromRequestToWeapp(ServerHttpRequest request, String tokenName) {
        return Optional.ofNullable(
                ObjectUtils.firstNonNull(
                        // 小程序优先从header直接取
                        request.getHeaders().getFirst(tokenName),
                        HttpUtils.getCookieValueByName(request, tokenName)
                ));
    }

    String getMerCodeFromRequest(ServerHttpRequest request) {
        String merCode = request.getHeaders().getFirst("merCode");
        MultiValueMap<String, String> queryParams = request.getQueryParams();
        System.out.println(queryParams);
        return Optional.ofNullable(request.getHeaders().getFirst("merCode"))
                .orElse(request.getQueryParams().getFirst("merCode"));
    }

    protected String getAppId(String merCode, PlatformEnum from, ServerHttpRequest request) {
        return multipleWeeChatMerConfiguration.isMultipleAppMerCode(merCode) && PlatformEnum.isWeeAppByEnumName(from.name()) ? request.getHeaders().getFirst(Const.HEAD_APPID_KEY) : null;

    }
}
