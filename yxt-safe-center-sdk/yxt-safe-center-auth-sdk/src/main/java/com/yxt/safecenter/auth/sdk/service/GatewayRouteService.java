package com.yxt.safecenter.auth.sdk.service;

import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionLocator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URI;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Since: 2025/2/25 15:00
 * Author: qs
 */

@Service
public class GatewayRouteService {

    @Resource
    private RouteDefinitionLocator routeDefinitionLocator;

    /**
     * 从网关中获取所有的路由配置，从 URI 中提取 host 作为 appName 列表
     */
    public List<String> getAppNames() {
        // 阻塞方式获取所有路由定义
        List<RouteDefinition> routes = routeDefinitionLocator.getRouteDefinitions().collectList().block();
        if (routes == null || routes.isEmpty()) {
            return Collections.emptyList();
        }
        // 从每个路由的 URI 中提取 host 作为 appName
        return routes.stream()
                .map(RouteDefinition::getUri)
                .filter(Objects::nonNull)
                .map(this::extractHost)
                .filter(appName -> appName != null && !appName.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 从 URI 中提取 host 部分作为 appName
     * 如 URI 为 lb://SERVICE-A 则返回 SERVICE-A
     */
    private String extractHost(URI uri) {
        // URI.getHost() 对于 lb:// 格式的 URI 通常能够解析出 host
        return uri.getHost();
    }
}
