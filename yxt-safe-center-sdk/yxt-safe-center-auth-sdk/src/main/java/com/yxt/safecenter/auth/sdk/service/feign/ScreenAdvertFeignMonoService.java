package com.yxt.safecenter.auth.sdk.service.feign;

import com.yxt.lang.dto.api.ResponseBase;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import javax.annotation.Resource;
import java.time.Duration;

/**
 * @author: Jack<PERSON>i
 * @methodsName:
 * @description: 开屏广告业务
 * @param:
 * @return:
 * @throws:
 **/
@Service
public class ScreenAdvertFeignMonoService {

    @Resource
    private ScreenAdvertFeignClient screenAdvertFeignClient;
    @Resource
    private Scheduler feignScheduler;

    public Mono<ResponseBase<Boolean>> setPopFlag(String tokenStr, String merCode) {
        return Mono.fromCallable(() -> screenAdvertFeignClient.setPopFlag(tokenStr, merCode))
                .timeout(Duration.ofSeconds(5))
                .subscribeOn(feignScheduler);
    }
}
