package com.yxt.safecenter.auth.sdk.service;

import com.yxt.safecenter.auth.sdk.dto.statistics.ApiStatisticsDTO;
import com.yxt.safecenter.auth.sdk.enums.AuthFailStatusEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * Since: 2025/2/13 11:55
 * Author: qs
 */

public class ApiStatisticsServiceUnitTest {

    public static void main(String[] args) {
        // 插入数据 模拟读取消息发送 清除消息
        ApiStatisticsService apiStatisticsService = new ApiStatisticsService();

        String key = "service1_/api/v1/resource_GET_AuthMethod_appKey123";

        // 请求次数增加1
//        apiStatisticsService.incrementRequestCount(key);
//        // 鉴权次数增加1
//        apiStatisticsService.incrementAuthFailCount(key);
//        // 请求次数增加1
//        apiStatisticsService.incrementRequestCount(key);

        // 获取数据
        Map<String, ApiStatisticsDTO> apiStatisticsMap = apiStatisticsService.getApiStatisticsMap();

        System.out.println(apiStatisticsMap);

    }

}
