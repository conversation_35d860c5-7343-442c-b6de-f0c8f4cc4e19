<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-safe-center-auth-sdk</artifactId>
    <name>yxt-safe-center-auth-sdk</name>
    <description>yxt-safe-center-auth-sdk</description>

    <parent>
        <groupId>com.yxt.safecenter</groupId>
        <artifactId>yxt-safe-center-sdk</artifactId>
        <version>${reversion}</version>
    </parent>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <module.deploy.skip>false</module.deploy.skip>
        <feign-reactor-spring-cloud-starter.version>3.2.6</feign-reactor-spring-cloud-starter.version>
        <fastjson.version>1.2.76</fastjson.version>
        <skywalking.version>8.2.0</skywalking.version>
        <alipay.version>3.0.52.ALL</alipay.version>
        <yxt-common-sign.version>4.3.1</yxt-common-sign.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yxt.safecenter</groupId>
            <artifactId>yxt-safe-center-common-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yxt.safecenter</groupId>
            <artifactId>yxt-safe-center-feign-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
            <version>3.0.3</version>
            <scope>provided</scope>
        </dependency>

        <!-- 响应式feign -->
        <dependency>
            <groupId>com.playtika.reactivefeign</groupId>
            <artifactId>feign-reactor-spring-cloud-starter</artifactId>
            <version>${feign-reactor-spring-cloud-starter.version}</version>
            <type>pom</type>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
            <version>${skywalking.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>${alipay.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-sign</artifactId>
            <version>${yxt-common-sign.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>2.1.5.RELEASE</version>
            <exclusions>
                <!-- 去掉对Lettuce的依赖，Spring Boot优先使用Lettuce作为Redis客户端-->
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.9</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
            <version>3.4.6</version>
            <scope>provided</scope>
        </dependency>

    </dependencies>

</project>
